# Comprehensive VS Code Process Scan
# فحص شامل لجميع عمليات VS Code والعمليات المرتبطة

Write-Host "🔍 COMPREHENSIVE VS CODE PROCESS SCAN" -ForegroundColor Red
Write-Host "=======================================" -ForegroundColor Red
Write-Host ""

# البحث عن جميع العمليات المرتبطة بـ VS Code
$allProcesses = Get-Process | Where-Object { 
    $_.ProcessName -like "*code*" -or 
    $_.ProcessName -like "*electron*" -or
    $_.ProcessName -like "*node*" -or
    $_.ProcessName -like "*typescript*" -or
    $_.ProcessName -like "*eslint*" -or
    $_.ProcessName -like "*python*" -or
    $_.ProcessName -like "*git*" -or
    $_.ProcessName -like "*rg*" -or
    $_.MainWindowTitle -like "*Visual Studio Code*" -or
    $_.Path -like "*Code*" -or
    $_.CommandLine -like "*code*"
}

# تصنيف العمليات
$codeProcesses = @()
$electronProcesses = @()
$nodeProcesses = @()
$languageServers = @()
$otherRelated = @()

foreach ($proc in $allProcesses) {
    try {
        $procName = $proc.ProcessName.ToLower()
        $procPath = ""
        $procCommandLine = ""
        
        try {
            $procPath = $proc.Path
            $procCommandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($proc.Id)").CommandLine
        } catch {}
        
        if ($procName -like "*code*" -and ($procPath -like "*Code*" -or $procCommandLine -like "*code*")) {
            $codeProcesses += $proc
        }
        elseif ($procName -like "*electron*") {
            $electronProcesses += $proc
        }
        elseif ($procName -like "*node*" -and ($procCommandLine -like "*typescript*" -or $procCommandLine -like "*eslint*" -or $procCommandLine -like "*language*")) {
            $languageServers += $proc
        }
        elseif ($procName -like "*node*") {
            $nodeProcesses += $proc
        }
        else {
            $otherRelated += $proc
        }
    } catch {
        $otherRelated += $proc
    }
}

# حساب الإجماليات
$totalVSCodeRelated = $codeProcesses.Count + $electronProcesses.Count + $nodeProcesses.Count + $languageServers.Count + $otherRelated.Count
$totalMemoryMB = 0

foreach ($proc in $allProcesses) {
    try {
        $totalMemoryMB += [math]::Round($proc.WorkingSet / 1MB, 1)
    } catch {}
}

Write-Host "📊 DETAILED BREAKDOWN:" -ForegroundColor Yellow
Write-Host "======================" -ForegroundColor Yellow
Write-Host "VS Code Core Processes: $($codeProcesses.Count)" -ForegroundColor White
Write-Host "Electron Processes: $($electronProcesses.Count)" -ForegroundColor White
Write-Host "Node.js Processes: $($nodeProcesses.Count)" -ForegroundColor White
Write-Host "Language Servers: $($languageServers.Count)" -ForegroundColor White
Write-Host "Other Related: $($otherRelated.Count)" -ForegroundColor White
Write-Host "-----------------------------------" -ForegroundColor Gray
Write-Host "TOTAL VS CODE RELATED: $totalVSCodeRelated" -ForegroundColor Cyan
Write-Host "Total Memory Usage: $totalMemoryMB MB" -ForegroundColor Cyan
Write-Host ""

# تحديد مستوى الخطر بناءً على العدد الإجمالي
if ($totalVSCodeRelated -ge 67) {
    Write-Host "🔴 CRITICAL EMERGENCY - 67+ PROCESSES!" -ForegroundColor Red
    Write-Host "This confirms your observation of 67 processes!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🚨 IMMEDIATE EMERGENCY ACTIONS:" -ForegroundColor Red
    Write-Host "1. 💾 SAVE ALL WORK IMMEDIATELY!" -ForegroundColor Yellow
    Write-Host "2. 🔴 Close ALL VS Code windows NOW!" -ForegroundColor Yellow
    Write-Host "3. ⏳ Wait 60 seconds for cleanup" -ForegroundColor Yellow
    Write-Host "4. 🔄 Restart computer if needed" -ForegroundColor Yellow
    Write-Host "5. ⚡ Start VS Code with: code --disable-extensions" -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "🛠️ EMERGENCY COMMANDS:" -ForegroundColor Magenta
    Write-Host "Force close all: taskkill /IM Code.exe /F /T" -ForegroundColor White
    Write-Host "Kill all related: Get-Process *code* | Stop-Process -Force" -ForegroundColor White
}
elseif ($totalVSCodeRelated -gt 50) {
    Write-Host "🟠 HIGH DANGER!" -ForegroundColor Red
    Write-Host "Very high process count - immediate action needed!" -ForegroundColor Yellow
}
elseif ($totalVSCodeRelated -gt 30) {
    Write-Host "🟡 MODERATE LOAD" -ForegroundColor Yellow
}
else {
    Write-Host "🟢 NORMAL LOAD" -ForegroundColor Green
}

Write-Host ""
Write-Host "📋 DETAILED PROCESS LIST:" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

if ($codeProcesses.Count -gt 0) {
    Write-Host ""
    Write-Host "🔵 VS Code Core Processes ($($codeProcesses.Count)):" -ForegroundColor Blue
    $codeProcesses | Sort-Object WorkingSet -Descending | ForEach-Object {
        $memMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "   PID: $($_.Id) | Memory: $memMB MB | $($_.ProcessName)" -ForegroundColor White
    }
}

if ($electronProcesses.Count -gt 0) {
    Write-Host ""
    Write-Host "⚡ Electron Processes ($($electronProcesses.Count)):" -ForegroundColor Yellow
    $electronProcesses | Sort-Object WorkingSet -Descending | ForEach-Object {
        $memMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "   PID: $($_.Id) | Memory: $memMB MB | $($_.ProcessName)" -ForegroundColor White
    }
}

if ($nodeProcesses.Count -gt 0) {
    Write-Host ""
    Write-Host "🟢 Node.js Processes ($($nodeProcesses.Count)):" -ForegroundColor Green
    $nodeProcesses | Sort-Object WorkingSet -Descending | ForEach-Object {
        $memMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "   PID: $($_.Id) | Memory: $memMB MB | $($_.ProcessName)" -ForegroundColor White
    }
}

if ($languageServers.Count -gt 0) {
    Write-Host ""
    Write-Host "🔧 Language Servers ($($languageServers.Count)):" -ForegroundColor Magenta
    $languageServers | Sort-Object WorkingSet -Descending | ForEach-Object {
        $memMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "   PID: $($_.Id) | Memory: $memMB MB | $($_.ProcessName)" -ForegroundColor White
    }
}

if ($otherRelated.Count -gt 0) {
    Write-Host ""
    Write-Host "❓ Other Related Processes ($($otherRelated.Count)):" -ForegroundColor Gray
    $otherRelated | Sort-Object WorkingSet -Descending | ForEach-Object {
        $memMB = [math]::Round($_.WorkingSet / 1MB, 1)
        Write-Host "   PID: $($_.Id) | Memory: $memMB MB | $($_.ProcessName)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "=======================================" -ForegroundColor Red
Write-Host "Comprehensive scan completed at $(Get-Date)" -ForegroundColor Gray

if ($totalVSCodeRelated -ge 60) {
    Write-Host ""
    Write-Host "🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨" -ForegroundColor Red
    Write-Host "   CRITICAL: $totalVSCodeRelated VS CODE PROCESSES!" -ForegroundColor Red
    Write-Host "   This is an emergency situation!" -ForegroundColor Red
    Write-Host "   Take immediate action!" -ForegroundColor Red
    Write-Host "🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
