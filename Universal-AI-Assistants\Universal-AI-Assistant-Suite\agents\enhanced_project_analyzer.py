#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 وكيل تحليل المشاريع المحسن
Enhanced Project Analyzer Agent
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from base_agent import BaseAgent

class EnhancedProjectAnalyzerAgent(BaseAgent):
    """📊 وكيل تحليل المشاريع المحسن"""
    
    def get_agent_type(self) -> str:
        return "enhanced_project_analyzer"
    
    def initialize_agent(self):
        """تهيئة وكيل تحليل المشاريع"""
        self.project_types = {
            'react': {
                'files': ['package.json', 'src', 'components'],
                'description': 'مشروع React'
            },
            'vue': {
                'files': ['package.json', 'src', 'components', 'vue.config.js'],
                'description': 'مشروع Vue.js'
            },
            'django': {
                'files': ['manage.py', 'settings.py', 'models.py'],
                'description': 'مشروع Django'
            },
            'fastapi': {
                'files': ['main.py', 'requirements.txt', 'app'],
                'description': 'مشروع FastAPI'
            },
            'nextjs': {
                'files': ['package.json', 'next.config.js', 'pages'],
                'description': 'مشروع Next.js'
            }
        }
        
        self.log_action("تهيئة محلل المشاريع المحسن", "جاهز للتحليل الشامل")
    
    def analyze_project(self, project_path=None):
        """تحليل شامل للمشروع"""
        target_path = Path(project_path) if project_path else self.project_path
        
        if not target_path.exists():
            return {'error': f'المشروع غير موجود: {target_path}'}
        
        analysis = {
            'project_path': str(target_path),
            'project_type': self._detect_project_type(target_path),
            'files_count': self._count_files(target_path),
            'structure': self._analyze_structure(target_path),
            'technologies': self._detect_technologies(target_path),
            'quality_score': self._calculate_quality_score(target_path),
            'recommendations': self._generate_recommendations(target_path)
        }
        
        self.log_action("تحليل المشروع", f"نوع المشروع: {analysis['project_type']}")
        return analysis
    
    def _detect_project_type(self, path):
        """كشف نوع المشروع"""
        for proj_type, config in self.project_types.items():
            indicators = config['files']
            matches = sum(1 for indicator in indicators if (path / indicator).exists())
            if matches >= len(indicators) // 2:  # نصف المؤشرات على الأقل
                return proj_type
        return 'unknown'
    
    def _count_files(self, path):
        """عد الملفات"""
        try:
            return len(list(path.rglob('*'))) if path.exists() else 0
        except:
            return 0
    
    def _analyze_structure(self, path):
        """تحليل هيكل المشروع"""
        structure = {}
        try:
            for item in path.iterdir():
                if item.name.startswith('.'):
                    continue
                if item.is_dir():
                    structure[item.name] = {
                        'type': 'directory',
                        'files_count': len(list(item.rglob('*')))
                    }
                else:
                    structure[item.name] = {
                        'type': 'file',
                        'size': item.stat().st_size
                    }
        except:
            pass
        return structure
    
    def _detect_technologies(self, path):
        """كشف التقنيات المستخدمة"""
        technologies = []
        
        # فحص ملفات التكوين
        config_files = {
            'package.json': 'Node.js/JavaScript',
            'requirements.txt': 'Python',
            'Dockerfile': 'Docker',
            'docker-compose.yml': 'Docker Compose'
        }
        
        for file_name, tech in config_files.items():
            if (path / file_name).exists():
                technologies.append(tech)
        
        return technologies
    
    def _calculate_quality_score(self, path):
        """حساب نقاط الجودة"""
        score = 0
        
        # فحص وجود ملفات مهمة
        important_files = ['README.md', 'LICENSE', '.gitignore']
        for file_name in important_files:
            if (path / file_name).exists():
                score += 20
        
        # فحص وجود مجلدات الاختبار
        test_folders = ['test', 'tests', '__tests__']
        for folder in test_folders:
            if (path / folder).exists():
                score += 30
                break
        
        return min(score, 100)
    
    def _generate_recommendations(self, path):
        """إنتاج توصيات التحسين"""
        recommendations = []
        
        if not (path / 'README.md').exists():
            recommendations.append("إضافة ملف README.md للتوثيق")
        
        if not (path / '.gitignore').exists():
            recommendations.append("إضافة ملف .gitignore")
        
        test_folders = ['test', 'tests', '__tests__']
        if not any((path / folder).exists() for folder in test_folders):
            recommendations.append("إضافة مجلد للاختبارات")
        
        if not recommendations:
            recommendations.append("المشروع منظم بشكل جيد!")
        
        return recommendations
