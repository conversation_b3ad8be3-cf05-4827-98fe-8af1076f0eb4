{"default_provider": "ollama", "providers": {"ollama": {"enabled": true, "model": "llama3:8b", "host": "localhost", "port": 11434, "description": "نموذج <PERSON><PERSON><PERSON> المحلي - مجاني ولا يحتاج إنترنت"}, "gemini": {"enabled": false, "model": "gemini-pro", "api_key": "", "description": "نموذج Google Gemini - يحتاج مفتاح API"}, "openai": {"enabled": false, "model": "gpt-3.5-turbo", "api_key": "", "description": "نموذج OpenAI GPT - يحتاج مفتاح API ومدفوع"}}, "agent_prompts": {"error_detector": {"system_prompt": "أنت خبير في كشف الأخطاء البرمجية. قم بتحليل الكود المعطى واكتشف الأخطاء والمشاكل المحتملة.", "analysis_prompt": "حلل هذا الكود واكتشف الأخطاء:\n\n{code}\n\nقدم تقريراً مفصلاً عن الأخطاء والحلول المقترحة."}, "project_analyzer": {"system_prompt": "أنت خبير في تحليل المشاريع البرمجية. قم بتحليل هيكل المشروع وتقديم توصيات للتحسين.", "analysis_prompt": "حلل هذا المشروع:\n\nالملفات: {files}\nالهيكل: {structure}\nالإحصائيات: {stats}\n\nقدم تحليلاً شاملاً وتوصيات للتحسين."}, "file_organizer": {"system_prompt": "أنت خبير في تنظيم الملفات والمجلدات. ساعد في تنظيم المشاريع بطريقة منطقية وفعالة.", "organization_prompt": "اقترح تنظيماً أفضل لهذه الملفات:\n\n{files}\n\nقدم هيكلاً منظماً ومبررات للتنظيم المقترح."}, "database_agent": {"system_prompt": "أنت خبير في قواعد البيانات وSQL. ساعد في تحسين الاستعلامات وتصميم قواعد البيانات.", "query_prompt": "حلل هذا الاستعلام واقترح تحسينات:\n\n{query}\n\nقدم تحليلاً للأداء واقتراحات للتحسين."}, "memory_agent": {"system_prompt": "أنت نظام ذاكرة ذكي. ساعد في تنظيم وإدارة المعلومات بطريقة فعالة.", "memory_prompt": "نظم هذه المعلومات في الذاكرة:\n\n{data}\n\nاقترح طريقة تنظيم وفهرسة فعالة."}}, "response_settings": {"max_tokens": 1000, "temperature": 0.7, "timeout": 60, "language": "arabic", "format": "markdown"}, "features": {"code_analysis": true, "project_insights": true, "auto_suggestions": true, "smart_organization": true, "intelligent_search": true, "context_awareness": true}}