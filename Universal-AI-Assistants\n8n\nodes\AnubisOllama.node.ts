import {
	IExecuteFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	NodeOperationError,
} from 'n8n-workflow';

export class AnubisOllama implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Anubis Ollama',
		name: 'anubisOllama',
		icon: 'file:anubis.svg',
		group: ['anubis'],
		version: 1,
		subtitle: '={{$parameter["model"]}}',
		description: 'Interact with Ollama models through Anubis API',
		defaults: {
			name: 'Anubis Ollama',
		},
		inputs: ['main'],
		outputs: ['main'],
		credentials: [
			{
				name: 'anubis<PERSON><PERSON>',
				required: true,
			},
		],
		properties: [
			{
				displayName: 'Model',
				name: 'model',
				type: 'options',
				options: [
					{
						name: 'Llama3 8B',
						value: 'llama3:8b',
						description: 'Best for Arabic and general analysis',
					},
					{
						name: 'Mistral 7B',
						value: 'mistral:7b',
						description: 'Best for English and code analysis',
					},
					{
						name: 'Phi3 Mini',
						value: 'phi3:mini',
						description: 'Fast and lightweight model',
					},
				],
				default: 'llama3:8b',
				description: 'The Ollama model to use',
			},
			{
				displayName: 'Prompt',
				name: 'prompt',
				type: 'string',
				typeOptions: {
					rows: 4,
				},
				default: '',
				placeholder: 'Enter your prompt here...',
				description: 'The prompt to send to the model',
			},
			{
				displayName: 'Temperature',
				name: 'temperature',
				type: 'number',
				default: 0.7,
				typeOptions: {
					minValue: 0,
					maxValue: 2,
					numberStepSize: 0.1,
				},
				description: 'Controls randomness in the response (0 = deterministic, 2 = very random)',
			},
			{
				displayName: 'Max Tokens',
				name: 'maxTokens',
				type: 'number',
				default: 1000,
				typeOptions: {
					minValue: 1,
					maxValue: 4096,
				},
				description: 'Maximum number of tokens to generate',
			},
			{
				displayName: 'Use Input Data',
				name: 'useInputData',
				type: 'boolean',
				default: false,
				description: 'Whether to use data from previous node as context',
			},
		],
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const returnData: INodeExecutionData[] = [];

		for (let i = 0; i < items.length; i++) {
			try {
				const model = this.getNodeParameter('model', i) as string;
				let prompt = this.getNodeParameter('prompt', i) as string;
				const temperature = this.getNodeParameter('temperature', i) as number;
				const maxTokens = this.getNodeParameter('maxTokens', i) as number;
				const useInputData = this.getNodeParameter('useInputData', i) as boolean;

				// إضافة بيانات الإدخال إلى الـ prompt إذا كان مطلوباً
				if (useInputData && items[i].json) {
					const inputContext = JSON.stringify(items[i].json, null, 2);
					prompt = `Context from previous node:\n${inputContext}\n\nPrompt: ${prompt}`;
				}

				// إعداد طلب API
				const requestBody = {
					prompt,
					model,
					temperature,
					max_tokens: maxTokens,
				};

				// استدعاء Anubis API
				const response = await this.helpers.httpRequestWithAuthentication.call(
					this,
					'anubisApi',
					{
						method: 'POST',
						url: '/api/v1/models/ollama/generate',
						body: requestBody,
						json: true,
					},
				);

				// إضافة النتيجة
				returnData.push({
					json: {
						model: response.model,
						prompt: response.prompt,
						response: response.response,
						responseTime: response.response_time,
						tokensGenerated: response.tokens_generated,
						status: response.status,
						timestamp: new Date().toISOString(),
					},
				});

			} catch (error) {
				if (this.continueOnFail()) {
					returnData.push({
						json: {
							error: error.message,
							status: 'error',
							timestamp: new Date().toISOString(),
						},
					});
					continue;
				}
				throw new NodeOperationError(this.getNode(), error);
			}
		}

		return [returnData];
	}
}
