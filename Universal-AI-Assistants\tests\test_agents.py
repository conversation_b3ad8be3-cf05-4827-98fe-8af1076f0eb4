#!/usr/bin/env python3
"""
اختبار تحميل الوكلاء
"""

import os
import sys
import importlib.util
from pathlib import Path

# إضافة المسارات
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'agents'))

def test_base_agent():
    """اختبار BaseAgent"""
    try:
        from core.base_agent import BaseAgent
        print("✅ تم تحميل BaseAgent")
        return True
    except Exception as e:
        print(f"❌ خطأ في تحميل BaseAgent: {e}")
        return False

def test_individual_agents():
    """اختبار الوكلاء الفرديين"""
    agents_dir = Path("../agents")
    
    for agent_file in agents_dir.glob("*_agent.py"):
        try:
            agent_name = agent_file.stem
            print(f"\n🧪 اختبار {agent_name}...")
            
            # تحميل الوحدة
            spec = importlib.util.spec_from_file_location(agent_name, agent_file)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # البحث عن فئة الوكيل
            agent_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if isinstance(attr, type) and attr_name.endswith('Agent') and attr_name != 'BaseAgent':
                    agent_class = attr
                    break
            
            if agent_class:
                print(f"✅ تم العثور على فئة: {agent_class.__name__}")
                
                # اختبار إنشاء مثيل
                try:
                    instance = agent_class("../Crestal Diamond", {}, False)
                    print(f"✅ تم إنشاء مثيل من {agent_class.__name__}")
                    print(f"   نوع الوكيل: {instance.get_agent_type()}")
                except Exception as e:
                    print(f"❌ خطأ في إنشاء مثيل: {e}")
            else:
                print(f"❌ لم يتم العثور على فئة وكيل في {agent_name}")
                
        except Exception as e:
            print(f"❌ خطأ في تحميل {agent_file.name}: {e}")
            import traceback
            traceback.print_exc()

def main():
    print("🧪 اختبار تحميل الوكلاء")
    print("=" * 50)
    
    # اختبار BaseAgent
    if not test_base_agent():
        return
    
    # اختبار الوكلاء الفرديين
    test_individual_agents()

if __name__ == "__main__":
    main()
