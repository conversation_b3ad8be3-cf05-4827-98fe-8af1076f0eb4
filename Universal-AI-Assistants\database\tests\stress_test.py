#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 اختبار الضغط لقاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Database Stress Test

اختبار أداء قاعدة البيانات تحت الضغط والحمولة العالية
"""

import mysql.connector
from mysql.connector import Error
import json
import os
import sys
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import random
import string

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DatabaseStressTester:
    """🔥 فئة اختبار الضغط لقاعدة البيانات"""
    
    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة اختبار الضغط"""
        self.config_path = config_path
        self.config = self._load_config()
        self.results = []
        self.errors = []
        self.start_time = None
        
    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config['database']['mysql']
        except Exception as e:
            raise Exception(f"خطأ في تحميل الإعدادات: {e}")
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config.get('charset', 'utf8mb4'),
                autocommit=True
            )
            return connection
        except Error as e:
            raise Exception(f"خطأ في الاتصال: {e}")
    
    def generate_random_string(self, length: int = 10) -> str:
        """إنتاج نص عشوائي"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def insert_test_projects(self, count: int = 100) -> Dict[str, Any]:
        """إدراج مشاريع اختبارية"""
        print(f"🔄 إدراج {count} مشروع اختباري...")
        
        start_time = time.time()
        successful_inserts = 0
        failed_inserts = 0
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            for i in range(count):
                try:
                    project_name = f"مشروع اختبار {self.generate_random_string(8)}"
                    project_path = f"/test/path/{self.generate_random_string(12)}"
                    project_type = random.choice(['python', 'javascript', 'java', 'cpp', 'go'])
                    description = f"وصف مشروع اختباري {self.generate_random_string(20)}"
                    
                    cursor.execute("""
                    INSERT INTO projects (name, path, type, description)
                    VALUES (%s, %s, %s, %s)
                    """, (project_name, project_path, project_type, description))
                    
                    successful_inserts += 1
                    
                    if (i + 1) % 20 == 0:
                        print(f"   ✅ تم إدراج {i + 1} مشروع")
                        
                except Exception as e:
                    failed_inserts += 1
                    self.errors.append(f"Insert error {i}: {e}")
            
            cursor.close()
            connection.close()
            
        except Exception as e:
            self.errors.append(f"Connection error during insert: {e}")
        
        duration = time.time() - start_time
        
        result = {
            'test': 'insert_projects',
            'count': count,
            'successful': successful_inserts,
            'failed': failed_inserts,
            'duration': duration,
            'rate': successful_inserts / duration if duration > 0 else 0
        }
        
        print(f"✅ إدراج المشاريع: {successful_inserts}/{count} نجح في {duration:.2f}s ({result['rate']:.1f} مشروع/ثانية)")
        
        return result
    
    def concurrent_read_test(self, thread_count: int = 10, queries_per_thread: int = 50) -> Dict[str, Any]:
        """اختبار القراءة المتزامنة"""
        print(f"🔄 اختبار القراءة المتزامنة: {thread_count} خيط، {queries_per_thread} استعلام لكل خيط...")
        
        results = []
        errors = []
        
        def read_worker(worker_id: int):
            """دالة العامل للقراءة"""
            worker_results = []
            worker_errors = []
            
            try:
                connection = self.get_connection()
                cursor = connection.cursor(dictionary=True)
                
                for i in range(queries_per_thread):
                    try:
                        start = time.time()
                        
                        # استعلامات متنوعة
                        query_type = random.choice(['projects', 'analyses', 'errors', 'complex'])
                        
                        if query_type == 'projects':
                            cursor.execute("SELECT * FROM projects ORDER BY created_at DESC LIMIT 10")
                        elif query_type == 'analyses':
                            cursor.execute("SELECT * FROM analyses ORDER BY score DESC LIMIT 10")
                        elif query_type == 'errors':
                            cursor.execute("SELECT * FROM errors ORDER BY created_at DESC LIMIT 10")
                        else:  # complex
                            cursor.execute("""
                            SELECT p.name, COUNT(a.id) as analyses_count, AVG(a.score) as avg_score
                            FROM projects p
                            LEFT JOIN analyses a ON p.id = a.project_id
                            GROUP BY p.id, p.name
                            ORDER BY avg_score DESC
                            LIMIT 5
                            """)
                        
                        results_data = cursor.fetchall()
                        duration = time.time() - start
                        
                        worker_results.append({
                            'worker_id': worker_id,
                            'query_id': i,
                            'query_type': query_type,
                            'duration': duration,
                            'result_count': len(results_data)
                        })
                        
                    except Exception as e:
                        worker_errors.append(f"Worker {worker_id}, Query {i}: {e}")
                
                cursor.close()
                connection.close()
                
            except Exception as e:
                worker_errors.append(f"Worker {worker_id} connection error: {e}")
            
            return worker_results, worker_errors
        
        # تشغيل الخيوط
        start_time = time.time()
        threads = []
        
        for i in range(thread_count):
            thread = threading.Thread(target=lambda i=i: results.extend(read_worker(i)[0]) or errors.extend(read_worker(i)[1]))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        duration = time.time() - start_time
        total_queries = len(results)
        
        result = {
            'test': 'concurrent_read',
            'thread_count': thread_count,
            'queries_per_thread': queries_per_thread,
            'total_queries': total_queries,
            'successful_queries': total_queries,
            'failed_queries': len(errors),
            'duration': duration,
            'queries_per_second': total_queries / duration if duration > 0 else 0,
            'avg_query_time': sum(r['duration'] for r in results) / total_queries if total_queries > 0 else 0
        }
        
        print(f"✅ القراءة المتزامنة: {total_queries} استعلام في {duration:.2f}s ({result['queries_per_second']:.1f} استعلام/ثانية)")
        print(f"   📊 متوسط وقت الاستعلام: {result['avg_query_time']:.4f}s")
        
        return result
    
    def bulk_data_test(self, batch_size: int = 1000) -> Dict[str, Any]:
        """اختبار إدراج البيانات بالدفعات"""
        print(f"🔄 اختبار إدراج البيانات بالدفعات: {batch_size} سجل...")
        
        start_time = time.time()
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # إعداد البيانات
            projects_data = []
            for i in range(batch_size):
                projects_data.append((
                    f"مشروع دفعي {self.generate_random_string(8)}",
                    f"/bulk/path/{self.generate_random_string(12)}",
                    random.choice(['python', 'javascript', 'java', 'cpp', 'go']),
                    f"وصف مشروع دفعي {self.generate_random_string(20)}"
                ))
            
            # إدراج البيانات بالدفعة
            cursor.executemany("""
            INSERT INTO projects (name, path, type, description)
            VALUES (%s, %s, %s, %s)
            """, projects_data)
            
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            
            result = {
                'test': 'bulk_insert',
                'batch_size': batch_size,
                'duration': duration,
                'rate': batch_size / duration if duration > 0 else 0
            }
            
            print(f"✅ الإدراج بالدفعات: {batch_size} سجل في {duration:.2f}s ({result['rate']:.1f} سجل/ثانية)")
            
            return result
            
        except Exception as e:
            self.errors.append(f"Bulk insert error: {e}")
            print(f"❌ فشل الإدراج بالدفعات: {e}")
            return {'test': 'bulk_insert', 'error': str(e)}
    
    def cleanup_test_data(self) -> int:
        """تنظيف البيانات الاختبارية"""
        print("🧹 تنظيف البيانات الاختبارية...")
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # حذف المشاريع الاختبارية
            cursor.execute("""
            DELETE FROM projects 
            WHERE name LIKE 'مشروع اختبار%' OR name LIKE 'مشروع دفعي%'
            """)
            
            deleted_count = cursor.rowcount
            
            cursor.close()
            connection.close()
            
            print(f"✅ تم حذف {deleted_count} مشروع اختباري")
            return deleted_count
            
        except Exception as e:
            self.errors.append(f"Cleanup error: {e}")
            print(f"❌ فشل في التنظيف: {e}")
            return 0
    
    def run_stress_tests(self) -> Dict[str, Any]:
        """تشغيل جميع اختبارات الضغط"""
        print("🚀 بدء اختبارات الضغط لقاعدة بيانات نظام أنوبيس")
        print("=" * 60)
        
        self.start_time = time.time()
        test_results = []
        
        # اختبار 1: إدراج مشاريع
        result1 = self.insert_test_projects(100)
        test_results.append(result1)
        
        print()
        
        # اختبار 2: القراءة المتزامنة
        result2 = self.concurrent_read_test(10, 50)
        test_results.append(result2)
        
        print()
        
        # اختبار 3: الإدراج بالدفعات
        result3 = self.bulk_data_test(1000)
        test_results.append(result3)
        
        print()
        
        # تنظيف البيانات
        deleted_count = self.cleanup_test_data()
        
        total_duration = time.time() - self.start_time
        
        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج اختبار الضغط")
        print("=" * 60)
        
        print(f"⏱️ إجمالي وقت الاختبار: {total_duration:.2f} ثانية")
        print(f"🧹 تم حذف {deleted_count} سجل اختباري")
        
        if self.errors:
            print(f"\n❌ الأخطاء ({len(self.errors)}):")
            for i, error in enumerate(self.errors[:5], 1):  # عرض أول 5 أخطاء فقط
                print(f"  {i}. {error}")
            if len(self.errors) > 5:
                print(f"  ... و {len(self.errors) - 5} خطأ آخر")
        else:
            print("\n✅ لم يتم العثور على أخطاء!")
        
        # إنشاء التقرير النهائي
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'total_duration': total_duration,
            'test_results': test_results,
            'errors_count': len(self.errors),
            'errors': self.errors,
            'cleanup': {
                'deleted_records': deleted_count
            },
            'database_config': {
                'host': self.config['host'],
                'port': self.config['port'],
                'database': self.config['database']
            }
        }
        
        return final_report


def main():
    """الدالة الرئيسية"""
    tester = DatabaseStressTester()
    report = tester.run_stress_tests()
    
    # حفظ التقرير
    report_file = f"database/stress_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 تم حفظ تقرير اختبار الضغط في: {report_file}")
    
    # تحديد كود الخروج
    exit_code = 0 if len(report['errors']) == 0 else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
