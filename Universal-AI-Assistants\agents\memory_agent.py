#!/usr/bin/env python3
"""
🧠 وكيل الذاكرة الذكي العالمي
Universal Intelligent Memory Agent

تم تطويره من نظام Collaborative_Workspace في مشروع Crestal Diamond
وتعميمه ليعمل مع أي مشروع
"""

import os
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

# إضافة مجلد core إلى المسار
import sys
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent

class MemoryAgent(BaseAgent):
    """وكيل ذكي مسؤول عن إدارة الذاكرة والمعرفة"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "memory"
    
    def initialize_agent(self):
        """تهيئة وكيل الذاكرة"""
        # إعدادات الذاكرة
        self.auto_save = self.config.get('auto_save', True)
        self.memory_retention_days = self.config.get('memory_retention_days', 30)
        self.max_memory_size_mb = self.config.get('max_memory_size_mb', 100)
        
        # مجلدات الذاكرة
        self.memory_dir = self.workspace_dir / "shared_memory"
        self.collaboration_dir = self.workspace_dir / "collaboration_logs"
        self.knowledge_dir = self.workspace_dir / "knowledge_base"
        
        # إنشاء المجلدات
        self.memory_dir.mkdir(exist_ok=True)
        self.collaboration_dir.mkdir(exist_ok=True)
        self.knowledge_dir.mkdir(exist_ok=True)
        
        # ملفات الذاكرة الأساسية
        self.shared_memory_file = self.memory_dir / "shared_memory.md"
        self.collaboration_log_file = self.collaboration_dir / "collaboration_log.md"
        self.knowledge_base_file = self.knowledge_dir / "knowledge_base.json"
        self.session_memory_file = self.memory_dir / f"session_{datetime.now().strftime('%Y%m%d')}.json"
        
        # تهيئة الملفات إذا لم تكن موجودة
        self._initialize_memory_files()
        
        self.log_action("تم تهيئة وكيل الذاكرة")
    
    def _initialize_memory_files(self):
        """تهيئة ملفات الذاكرة"""
        # الذاكرة المشتركة
        if not self.shared_memory_file.exists():
            self._create_shared_memory_template()
        
        # سجل التعاون
        if not self.collaboration_log_file.exists():
            self._create_collaboration_log_template()
        
        # قاعدة المعرفة
        if not self.knowledge_base_file.exists():
            self._create_knowledge_base_template()
        
        # ذاكرة الجلسة
        if not self.session_memory_file.exists():
            self._create_session_memory_template()
    
    def _create_shared_memory_template(self):
        """إنشاء قالب الذاكرة المشتركة"""
        template = f"""# الذاكرة المشتركة - {self.project_path.name}

## معلومات المشروع
- **اسم المشروع**: {self.project_path.name}
- **نوع المشروع**: {self.detect_project_type()}
- **تاريخ البدء**: {datetime.now().strftime('%Y-%m-%d')}
- **آخر تحديث**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## الحالة الحالية
- **الإصدار**: 1.0.0
- **الحالة**: قيد التطوير
- **الوكلاء النشطين**: Memory Agent

## المشاكل المكتشفة
- لا توجد مشاكل مكتشفة حتى الآن

## الإنجازات
- تم تهيئة نظام الذاكرة المشتركة

## المهام القادمة
- تحليل شامل للمشروع
- كشف المشاكل والأخطاء
- تحسين الأداء

## ملاحظات مهمة
- جميع الملفات تستخدم ترميز UTF-8
- النظام يدعم اللغة العربية

---
**آخر تحديث**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**المسؤول**: Memory Agent
"""
        
        with open(self.shared_memory_file, 'w', encoding='utf-8') as f:
            f.write(template)
    
    def _create_collaboration_log_template(self):
        """إنشاء قالب سجل التعاون"""
        template = f"""# سجل التعاون - {self.project_path.name}

## {datetime.now().strftime('%Y-%m-%d')} - الجلسة الحالية
- **Memory Agent**: تم تهيئة نظام الذاكرة المشتركة
- **Memory Agent**: إنشاء ملفات الذاكرة الأساسية

---
**بدء السجل**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open(self.collaboration_log_file, 'w', encoding='utf-8') as f:
            f.write(template)
    
    def _create_knowledge_base_template(self):
        """إنشاء قالب قاعدة المعرفة"""
        template = {
            "project_info": {
                "name": self.project_path.name,
                "type": self.detect_project_type(),
                "created_date": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            },
            "agents_knowledge": {
                "memory_agent": {
                    "capabilities": [
                        "إدارة الذاكرة المشتركة",
                        "توثيق التقدم",
                        "حفظ السياق التاريخي",
                        "إدارة المعرفة"
                    ],
                    "last_active": datetime.now().isoformat()
                }
            },
            "project_knowledge": {
                "technologies": [],
                "dependencies": [],
                "architecture": "unknown",
                "patterns": []
            },
            "issues_knowledge": {
                "known_issues": [],
                "solutions": [],
                "best_practices": []
            },
            "performance_knowledge": {
                "benchmarks": [],
                "optimizations": [],
                "bottlenecks": []
            }
        }
        
        with open(self.knowledge_base_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
    
    def _create_session_memory_template(self):
        """إنشاء قالب ذاكرة الجلسة"""
        template = {
            "session_info": {
                "date": datetime.now().strftime('%Y-%m-%d'),
                "start_time": datetime.now().isoformat(),
                "project": self.project_path.name
            },
            "agents_activity": {},
            "discoveries": [],
            "issues_found": [],
            "solutions_applied": [],
            "performance_metrics": {},
            "notes": []
        }
        
        with open(self.session_memory_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)
    
    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل الذاكرة"""
        analysis_result = {
            'memory_status': {},
            'knowledge_summary': {},
            'collaboration_summary': {},
            'recommendations': []
        }
        
        try:
            # فحص حالة الذاكرة
            analysis_result['memory_status'] = self.check_memory_status()
            
            # ملخص المعرفة
            analysis_result['knowledge_summary'] = self.get_knowledge_summary()
            
            # ملخص التعاون
            analysis_result['collaboration_summary'] = self.get_collaboration_summary()
            
            # إنشاء التوصيات
            analysis_result['recommendations'] = self.generate_memory_recommendations()
            
            # تحديث ذاكرة الجلسة
            self.update_session_memory('analysis_completed', analysis_result)
            
        except Exception as e:
            analysis_result['error'] = str(e)
            self.log_action("خطأ في تحليل الذاكرة", str(e))
        
        return analysis_result
    
    def check_memory_status(self) -> Dict[str, Any]:
        """فحص حالة الذاكرة"""
        status = {
            'files_exist': {},
            'total_size_mb': 0,
            'last_updated': {},
            'health': 'good'
        }
        
        memory_files = {
            'shared_memory': self.shared_memory_file,
            'collaboration_log': self.collaboration_log_file,
            'knowledge_base': self.knowledge_base_file,
            'session_memory': self.session_memory_file
        }
        
        for name, file_path in memory_files.items():
            status['files_exist'][name] = file_path.exists()
            
            if file_path.exists():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                status['total_size_mb'] += size_mb
                
                modified_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                status['last_updated'][name] = modified_time.isoformat()
        
        # فحص الصحة
        if status['total_size_mb'] > self.max_memory_size_mb:
            status['health'] = 'needs_cleanup'
        
        missing_files = [name for name, exists in status['files_exist'].items() if not exists]
        if missing_files:
            status['health'] = 'incomplete'
            status['missing_files'] = missing_files
        
        return status
    
    def get_knowledge_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص المعرفة"""
        summary = {
            'total_entries': 0,
            'categories': {},
            'recent_updates': []
        }
        
        try:
            if self.knowledge_base_file.exists():
                with open(self.knowledge_base_file, 'r', encoding='utf-8') as f:
                    knowledge = json.load(f)
                
                # عد الإدخالات
                for category, data in knowledge.items():
                    if isinstance(data, dict):
                        summary['categories'][category] = len(data)
                        summary['total_entries'] += len(data)
                    elif isinstance(data, list):
                        summary['categories'][category] = len(data)
                        summary['total_entries'] += len(data)
                
                # آخر التحديثات
                if 'project_info' in knowledge:
                    last_updated = knowledge['project_info'].get('last_updated')
                    if last_updated:
                        summary['last_updated'] = last_updated
        
        except Exception as e:
            summary['error'] = str(e)
        
        return summary
    
    def get_collaboration_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص التعاون"""
        summary = {
            'total_entries': 0,
            'agents_activity': {},
            'recent_activity': []
        }
        
        try:
            if self.collaboration_log_file.exists():
                content = self.collaboration_log_file.read_text(encoding='utf-8')
                
                # عد الإدخالات
                lines = content.split('\n')
                summary['total_entries'] = len([line for line in lines if line.strip().startswith('-')])
                
                # نشاط الوكلاء
                for line in lines:
                    if '**' in line and '**:' in line:
                        agent_name = line.split('**')[1].split('**')[0]
                        if agent_name not in summary['agents_activity']:
                            summary['agents_activity'][agent_name] = 0
                        summary['agents_activity'][agent_name] += 1
                
                # آخر النشاطات
                recent_lines = [line for line in lines[-10:] if line.strip().startswith('-')]
                summary['recent_activity'] = recent_lines
        
        except Exception as e:
            summary['error'] = str(e)
        
        return summary
    
    def generate_memory_recommendations(self) -> List[str]:
        """إنشاء توصيات الذاكرة"""
        recommendations = []
        
        try:
            status = self.check_memory_status()
            
            # توصيات بناء على الحالة
            if status['health'] == 'needs_cleanup':
                recommendations.append("تنظيف ملفات الذاكرة الكبيرة")
            
            if status['health'] == 'incomplete':
                recommendations.append("إنشاء الملفات المفقودة")
            
            # توصيات بناء على الحجم
            if status['total_size_mb'] > self.max_memory_size_mb * 0.8:
                recommendations.append("أرشفة الذاكرة القديمة")
            
            # توصيات عامة
            if status['total_size_mb'] == 0:
                recommendations.append("بدء توثيق المشروع")
            
            recommendations.append("تحديث الذاكرة المشتركة بانتظام")
            
        except Exception as e:
            recommendations.append(f"خطأ في إنشاء التوصيات: {e}")
        
        return recommendations
    
    def update_shared_memory(self, section: str, content: str):
        """تحديث الذاكرة المشتركة"""
        try:
            if self.shared_memory_file.exists():
                current_content = self.shared_memory_file.read_text(encoding='utf-8')
            else:
                current_content = ""
            
            # إضافة المحتوى الجديد
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            new_entry = f"\n## {section} - {timestamp}\n{content}\n"
            
            updated_content = current_content + new_entry
            
            with open(self.shared_memory_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            self.log_action("تحديث الذاكرة المشتركة", section)
            
        except Exception as e:
            self.log_action("خطأ في تحديث الذاكرة المشتركة", str(e))
    
    def update_collaboration_log(self, agent_name: str, action: str, details: str = ""):
        """تحديث سجل التعاون"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            entry = f"- **{agent_name}**: {action}"
            if details:
                entry += f" - {details}"
            entry += f" ({timestamp})\n"
            
            with open(self.collaboration_log_file, 'a', encoding='utf-8') as f:
                f.write(entry)
            
            self.log_action("تحديث سجل التعاون", f"{agent_name}: {action}")
            
        except Exception as e:
            self.log_action("خطأ في تحديث سجل التعاون", str(e))
    
    def update_session_memory(self, event_type: str, data: Any):
        """تحديث ذاكرة الجلسة"""
        try:
            if self.session_memory_file.exists():
                with open(self.session_memory_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
            else:
                session_data = self._create_session_memory_template()
            
            # إضافة الحدث
            timestamp = datetime.now().isoformat()
            
            if event_type == 'agent_activity':
                agent_name = data.get('agent_name', 'unknown')
                if agent_name not in session_data['agents_activity']:
                    session_data['agents_activity'][agent_name] = []
                
                session_data['agents_activity'][agent_name].append({
                    'timestamp': timestamp,
                    'action': data.get('action', ''),
                    'result': data.get('result', '')
                })
            
            elif event_type in ['discoveries', 'issues_found', 'solutions_applied', 'notes']:
                if event_type not in session_data:
                    session_data[event_type] = []
                
                session_data[event_type].append({
                    'timestamp': timestamp,
                    'data': data
                })
            
            # حفظ التحديث
            with open(self.session_memory_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.log_action("خطأ في تحديث ذاكرة الجلسة", str(e))
    
    def cleanup_old_memory(self):
        """تنظيف الذاكرة القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.memory_retention_days)
            
            # تنظيف ملفات الجلسات القديمة
            for file_path in self.memory_dir.glob("session_*.json"):
                if file_path.stat().st_mtime < cutoff_date.timestamp():
                    # أرشفة بدلاً من الحذف
                    archive_dir = self.memory_dir / "archive"
                    archive_dir.mkdir(exist_ok=True)
                    
                    archive_path = archive_dir / file_path.name
                    file_path.rename(archive_path)
                    
                    self.log_action("أرشفة ملف جلسة قديم", str(file_path))
            
        except Exception as e:
            self.log_action("خطأ في تنظيف الذاكرة القديمة", str(e))
