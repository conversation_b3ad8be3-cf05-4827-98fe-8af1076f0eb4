#!/usr/bin/env python3
"""
🌐 تطبيق Streamlit مثال
Example Streamlit Application

تطبيق ويب تفاعلي باستخدام Streamlit مع أفضل الممارسات
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json

# إعداد الصفحة
st.set_page_config(
    page_title="تطبيق Streamlit مثال",
    page_icon="🌐",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS مخصص
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
</style>
""", unsafe_allow_html=True)

def load_sample_data():
    """تحميل بيانات مثال"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
    data = {
        'التاريخ': dates,
        'المبيعات': np.random.randint(100, 1000, len(dates)),
        'الزوار': np.random.randint(50, 500, len(dates)),
        'التحويلات': np.random.randint(5, 50, len(dates))
    }
    return pd.DataFrame(data)

def create_metrics_dashboard(df):
    """إنشاء لوحة المقاييس"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_sales = df['المبيعات'].sum()
        st.metric(
            label="إجمالي المبيعات",
            value=f"{total_sales:,}",
            delta=f"{df['المبيعات'].iloc[-1] - df['المبيعات'].iloc[-2]:+,}"
        )
    
    with col2:
        avg_visitors = df['الزوار'].mean()
        st.metric(
            label="متوسط الزوار",
            value=f"{avg_visitors:.0f}",
            delta=f"{df['الزوار'].iloc[-1] - df['الزوار'].iloc[-2]:+.0f}"
        )
    
    with col3:
        total_conversions = df['التحويلات'].sum()
        st.metric(
            label="إجمالي التحويلات",
            value=f"{total_conversions:,}",
            delta=f"{df['التحويلات'].iloc[-1] - df['التحويلات'].iloc[-2]:+,}"
        )
    
    with col4:
        conversion_rate = (df['التحويلات'].sum() / df['الزوار'].sum()) * 100
        st.metric(
            label="معدل التحويل",
            value=f"{conversion_rate:.2f}%",
            delta="0.5%"
        )

def create_charts(df):
    """إنشاء الرسوم البيانية"""
    
    # رسم بياني للمبيعات عبر الزمن
    fig_sales = px.line(
        df, 
        x='التاريخ', 
        y='المبيعات',
        title='تطور المبيعات عبر الزمن',
        color_discrete_sequence=['#1f77b4']
    )
    fig_sales.update_layout(
        xaxis_title="التاريخ",
        yaxis_title="المبيعات",
        hovermode='x unified'
    )
    st.plotly_chart(fig_sales, use_container_width=True)
    
    # رسم بياني مقارن
    col1, col2 = st.columns(2)
    
    with col1:
        fig_bar = px.bar(
            df.tail(30), 
            x='التاريخ', 
            y='الزوار',
            title='الزوار في آخر 30 يوم',
            color='الزوار',
            color_continuous_scale='Blues'
        )
        st.plotly_chart(fig_bar, use_container_width=True)
    
    with col2:
        fig_scatter = px.scatter(
            df, 
            x='الزوار', 
            y='التحويلات',
            title='العلاقة بين الزوار والتحويلات',
            color='المبيعات',
            size='المبيعات',
            color_continuous_scale='Viridis'
        )
        st.plotly_chart(fig_scatter, use_container_width=True)

def sidebar_controls():
    """عناصر التحكم في الشريط الجانبي"""
    st.sidebar.header("⚙️ إعدادات التطبيق")
    
    # فلتر التاريخ
    date_range = st.sidebar.date_input(
        "اختر نطاق التاريخ",
        value=(datetime(2024, 1, 1), datetime(2024, 12, 31)),
        min_value=datetime(2024, 1, 1),
        max_value=datetime(2024, 12, 31)
    )
    
    # خيارات العرض
    show_raw_data = st.sidebar.checkbox("عرض البيانات الخام", value=False)
    chart_type = st.sidebar.selectbox(
        "نوع الرسم البياني",
        ["خطي", "عمودي", "نقطي"]
    )
    
    # إعدادات التصدير
    st.sidebar.header("📤 تصدير البيانات")
    export_format = st.sidebar.selectbox(
        "تنسيق التصدير",
        ["CSV", "Excel", "JSON"]
    )
    
    return date_range, show_raw_data, chart_type, export_format

def main():
    """الدالة الرئيسية للتطبيق"""
    
    # العنوان الرئيسي
    st.markdown('<h1 class="main-header">🌐 تطبيق Streamlit مثال</h1>', unsafe_allow_html=True)
    
    # الشريط الجانبي
    date_range, show_raw_data, chart_type, export_format = sidebar_controls()
    
    # تحميل البيانات
    with st.spinner('جاري تحميل البيانات...'):
        df = load_sample_data()
    
    # فلترة البيانات حسب التاريخ
    if len(date_range) == 2:
        start_date, end_date = date_range
        df_filtered = df[
            (df['التاريخ'].dt.date >= start_date) & 
            (df['التاريخ'].dt.date <= end_date)
        ]
    else:
        df_filtered = df
    
    # لوحة المقاييس
    st.header("📊 لوحة المقاييس")
    create_metrics_dashboard(df_filtered)
    
    st.divider()
    
    # الرسوم البيانية
    st.header("📈 الرسوم البيانية")
    create_charts(df_filtered)
    
    st.divider()
    
    # عرض البيانات الخام
    if show_raw_data:
        st.header("📋 البيانات الخام")
        st.dataframe(df_filtered, use_container_width=True)
        
        # زر التصدير
        if st.button("📤 تصدير البيانات"):
            if export_format == "CSV":
                csv = df_filtered.to_csv(index=False)
                st.download_button(
                    label="تحميل CSV",
                    data=csv,
                    file_name=f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            elif export_format == "JSON":
                json_data = df_filtered.to_json(orient='records', date_format='iso')
                st.download_button(
                    label="تحميل JSON",
                    data=json_data,
                    file_name=f"data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
    
    # معلومات التطبيق
    with st.expander("ℹ️ معلومات التطبيق"):
        st.write("""
        **تطبيق Streamlit مثال**
        
        هذا تطبيق مثال يوضح كيفية إنشاء تطبيق ويب تفاعلي باستخدام Streamlit.
        
        **المميزات:**
        - لوحة مقاييس تفاعلية
        - رسوم بيانية متقدمة
        - فلترة البيانات
        - تصدير البيانات
        - واجهة مستخدم عربية
        
        **التقنيات المستخدمة:**
        - Streamlit للواجهة
        - Plotly للرسوم البيانية
        - Pandas لمعالجة البيانات
        - NumPy للحسابات
        """)

if __name__ == "__main__":
    main()
