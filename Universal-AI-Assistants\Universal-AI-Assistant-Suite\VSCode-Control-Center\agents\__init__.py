# -*- coding: utf-8 -*-
"""
🤖 نظام الوكلاء الذكي - VS Code Control Center
===============================================

نظام متقدم من الوكلاء الذكيين لتحليل وتحسين أداء VS Code والنظام

الوكلاء المتاحون:
🔍 ProcessAnalyzerAgent - تحليل العمليات
⚡ PerformanceOptimizerAgent - تحسين الأداء  
🛡️ SecurityMonitorAgent - مراقبة الأمان
💡 SmartRecommendationsAgent - التوصيات الذكية
🤖 GeminiAgent - تحليل متقدم بـ Gemini
🦙 OllamaAgent - تحليل محلي بـ Ollama
"""

from .process_analyzer import ProcessAnalyzerAgent
from .performance_optimizer import PerformanceOptimizerAgent
from .security_monitor import SecurityMonitorAgent
from .smart_recommendations import SmartRecommendationsAgent
from .gemini_agent import GeminiAgent
from .ollama_agent import OllamaAgent
from .agent_coordinator import AgentCoordinator

__version__ = "1.0.0"
__author__ = "Universal AI Assistants"

# تصدير جميع الوكلاء
__all__ = [
    'ProcessAnalyzerAgent',
    'PerformanceOptimizerAgent', 
    'SecurityMonitorAgent',
    'SmartRecommendationsAgent',
    'GeminiAgent',
    'OllamaAgent',
    'AgentCoordinator'
]

# إعدادات الوكلاء الافتراضية
DEFAULT_AGENT_CONFIG = {
    'update_interval': 5,  # ثواني
    'max_analysis_depth': 3,
    'enable_auto_optimization': False,
    'language': 'ar',  # العربية كلغة افتراضية
    'gemini_model': 'gemini-pro',
    'ollama_model': 'llama2',
    'security_level': 'medium'
}

def create_agent_system():
    """إنشاء نظام الوكلاء الكامل"""
    return AgentCoordinator()

def get_available_agents():
    """الحصول على قائمة الوكلاء المتاحين"""
    return {
        'process_analyzer': '🔍 محلل العمليات',
        'performance_optimizer': '⚡ محسن الأداء',
        'security_monitor': '🛡️ مراقب الأمان', 
        'smart_recommendations': '💡 التوصيات الذكية',
        'gemini_agent': '🤖 وكيل Gemini',
        'ollama_agent': '🦙 وكيل Ollama'
    }
