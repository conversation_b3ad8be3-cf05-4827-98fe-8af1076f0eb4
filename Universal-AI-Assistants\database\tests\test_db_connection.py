#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 اختبار اتصال قاعدة البيانات
Database Connection Test
"""

import os
import sys
import json
from pathlib import Path

print("🔍 اختبار اتصال قاعدة البيانات لنظام أنوبيس")
print("=" * 50)

# فحص ملف التكوين
config_file = Path("configs/database_config.json")
print(f"\n📄 فحص ملف التكوين: {config_file}")

if config_file.exists():
    print("   ✅ ملف التكوين موجود")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        db_config = config.get('database', {})
        mysql_config = db_config.get('mysql', {})
        
        print(f"   📊 نوع قاعدة البيانات: {db_config.get('type', 'غير محدد')}")
        print(f"   🌐 المضيف: {mysql_config.get('host', 'غير محدد')}")
        print(f"   🔌 المنفذ: {mysql_config.get('port', 'غير محدد')}")
        print(f"   👤 المستخدم: {mysql_config.get('user', 'غير محدد')}")
        print(f"   🗄️ قاعدة البيانات: {mysql_config.get('database', 'غير محدد')}")
        
    except Exception as e:
        print(f"   ❌ خطأ في قراءة التكوين: {e}")
        sys.exit(1)
else:
    print("   ❌ ملف التكوين غير موجود")
    sys.exit(1)

# اختبار استيراد mysql.connector
print(f"\n📦 اختبار استيراد mysql.connector...")
try:
    import mysql.connector
    print("   ✅ mysql.connector متاح")
except ImportError:
    print("   ❌ mysql.connector غير متاح")
    print("   💡 قم بتثبيته: pip install mysql-connector-python")
    sys.exit(1)

# اختبار الاتصال
print(f"\n🌐 اختبار الاتصال بقاعدة البيانات...")
try:
    connection = mysql.connector.connect(
        host=mysql_config['host'],
        port=mysql_config['port'],
        user=mysql_config['user'],
        password=mysql_config['password'],
        database=mysql_config['database'],
        charset=mysql_config.get('charset', 'utf8mb4'),
        autocommit=True
    )
    
    print("   ✅ تم الاتصال بنجاح!")
    
    # اختبار استعلام بسيط
    cursor = connection.cursor()
    cursor.execute("SELECT VERSION()")
    version = cursor.fetchone()
    print(f"   📊 إصدار MySQL: {version[0]}")
    
    # فحص الجداول
    cursor.execute("SHOW TABLES")
    tables = cursor.fetchall()
    print(f"   📋 عدد الجداول: {len(tables)}")
    
    if tables:
        print("   📋 الجداول الموجودة:")
        for table in tables:
            print(f"      - {table[0]}")
    
    # إحصائيات بسيطة
    try:
        cursor.execute("SELECT COUNT(*) FROM projects")
        projects_count = cursor.fetchone()[0]
        print(f"   📁 عدد المشاريع: {projects_count}")
    except:
        print("   ⚠️ جدول المشاريع غير موجود")
    
    try:
        cursor.execute("SELECT COUNT(*) FROM analyses")
        analyses_count = cursor.fetchone()[0]
        print(f"   🔍 عدد التحليلات: {analyses_count}")
    except:
        print("   ⚠️ جدول التحليلات غير موجود")
    
    cursor.close()
    connection.close()
    
    print("\n🎉 قاعدة البيانات متصلة وتعمل بشكل صحيح!")
    
except mysql.connector.Error as e:
    print(f"   ❌ خطأ في الاتصال: {e}")
    print(f"\n💡 تحقق من:")
    print(f"   - تشغيل خادم MySQL")
    print(f"   - صحة بيانات الاتصال")
    print(f"   - وجود قاعدة البيانات")
    
except Exception as e:
    print(f"   ❌ خطأ عام: {e}")

# اختبار DatabaseAgent
print(f"\n🤖 اختبار DatabaseAgent...")
try:
    sys.path.append('.')
    from agents.database_agent import DatabaseAgent
    
    # إنشاء وكيل قاعدة البيانات
    db_agent = DatabaseAgent(".", config, True)
    
    # تشغيل تحليل
    result = db_agent.run_analysis()
    
    print(f"   ✅ DatabaseAgent يعمل")
    print(f"   📊 نوع قاعدة البيانات: {result.get('database_type', 'غير محدد')}")
    print(f"   🔗 اختبار الاتصال: {'✅ نجح' if result.get('connection_test') else '❌ فشل'}")
    
    if result.get('recommendations'):
        print(f"   💡 التوصيات:")
        for rec in result['recommendations'][:3]:
            print(f"      - {rec}")
    
except Exception as e:
    print(f"   ❌ خطأ في DatabaseAgent: {e}")

print(f"\n" + "="*50)
print(f"🏆 تقرير اتصال قاعدة البيانات")
print(f"="*50)

if 'connection' in locals():
    print(f"✅ الحالة: متصل بقاعدة البيانات")
    print(f"🗄️ النوع: MySQL")
    print(f"🌐 المضيف: {mysql_config['host']}:{mysql_config['port']}")
    print(f"🗃️ قاعدة البيانات: {mysql_config['database']}")
else:
    print(f"❌ الحالة: غير متصل")

print(f"\n🏺 نظام أنوبيس - فحص قاعدة البيانات مكتمل!")
