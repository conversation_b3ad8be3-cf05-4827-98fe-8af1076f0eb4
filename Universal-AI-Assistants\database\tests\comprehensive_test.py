#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار شامل لقاعدة بيانات نظام أنوبيس
Anubis AI Assistants System - Comprehensive Database Test

اختبار جميع وظائف قاعدة البيانات والتأكد من سلامة البيانات
"""

import mysql.connector
from mysql.connector import Error
import json
import os
import sys
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import time

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DatabaseTester:
    """🔬 فئة اختبار قاعدة البيانات"""
    
    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة اختبار قاعدة البيانات"""
        self.config_path = config_path
        self.config = self._load_config()
        self.test_results = []
        self.errors_found = []
        
    def _load_config(self) -> Dict[str, Any]:
        """تحميل إعدادات قاعدة البيانات"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config['database']['mysql']
        except Exception as e:
            raise Exception(f"خطأ في تحميل الإعدادات: {e}")
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            connection = mysql.connector.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config.get('charset', 'utf8mb4'),
                autocommit=True
            )
            return connection
        except Error as e:
            raise Exception(f"خطأ في الاتصال: {e}")
    
    def log_test(self, test_name: str, status: str, message: str = "", duration: float = 0):
        """تسجيل نتيجة اختبار"""
        result = {
            'test_name': test_name,
            'status': status,
            'message': message,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        duration_str = f" ({duration:.3f}s)" if duration > 0 else ""
        print(f"{status_icon} {test_name}{duration_str}")
        if message:
            print(f"   📝 {message}")
    
    def test_connection(self) -> bool:
        """اختبار الاتصال بقاعدة البيانات"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            self.log_test("اختبار الاتصال", "PASS", "الاتصال بقاعدة البيانات نجح", duration)
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار الاتصال", "FAIL", f"فشل الاتصال: {e}", duration)
            self.errors_found.append(f"Connection Error: {e}")
            return False
    
    def test_database_structure(self) -> bool:
        """اختبار هيكل قاعدة البيانات"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            # التحقق من وجود الجداول المطلوبة
            expected_tables = ['projects', 'analyses', 'errors', 'reports', 'plugins', 'activities']
            
            cursor.execute("SHOW TABLES")
            existing_tables = [table[0] for table in cursor.fetchall()]
            
            missing_tables = set(expected_tables) - set(existing_tables)
            extra_tables = set(existing_tables) - set(expected_tables)
            
            if missing_tables:
                duration = time.time() - start_time
                self.log_test("اختبار هيكل قاعدة البيانات", "FAIL", 
                            f"جداول مفقودة: {', '.join(missing_tables)}", duration)
                self.errors_found.append(f"Missing tables: {missing_tables}")
                return False
            
            # التحقق من أعمدة الجداول الرئيسية
            table_checks = {
                'projects': ['id', 'name', 'path', 'type', 'description', 'created_at', 'updated_at'],
                'analyses': ['id', 'project_id', 'agent_type', 'analysis_data', 'results', 'score', 'created_at'],
                'errors': ['id', 'project_id', 'file_path', 'line_number', 'error_type', 'severity', 'message', 'created_at']
            }
            
            for table_name, expected_columns in table_checks.items():
                cursor.execute(f"DESCRIBE {table_name}")
                existing_columns = [column[0] for column in cursor.fetchall()]
                
                missing_columns = set(expected_columns) - set(existing_columns)
                if missing_columns:
                    duration = time.time() - start_time
                    self.log_test("اختبار هيكل قاعدة البيانات", "FAIL", 
                                f"أعمدة مفقودة في جدول {table_name}: {', '.join(missing_columns)}", duration)
                    self.errors_found.append(f"Missing columns in {table_name}: {missing_columns}")
                    return False
            
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            self.log_test("اختبار هيكل قاعدة البيانات", "PASS", 
                        f"جميع الجداول والأعمدة موجودة ({len(existing_tables)} جداول)", duration)
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار هيكل قاعدة البيانات", "FAIL", f"خطأ: {e}", duration)
            self.errors_found.append(f"Structure Error: {e}")
            return False
    
    def test_data_integrity(self) -> bool:
        """اختبار سلامة البيانات"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # التحقق من وجود بيانات في الجداول الرئيسية
            data_checks = []
            
            # فحص المشاريع
            cursor.execute("SELECT COUNT(*) as count FROM projects")
            projects_count = cursor.fetchone()['count']
            data_checks.append(f"المشاريع: {projects_count}")
            
            # فحص التحليلات
            cursor.execute("SELECT COUNT(*) as count FROM analyses")
            analyses_count = cursor.fetchone()['count']
            data_checks.append(f"التحليلات: {analyses_count}")
            
            # فحص الأخطاء
            cursor.execute("SELECT COUNT(*) as count FROM errors")
            errors_count = cursor.fetchone()['count']
            data_checks.append(f"الأخطاء: {errors_count}")
            
            # فحص الإضافات
            cursor.execute("SELECT COUNT(*) as count FROM plugins")
            plugins_count = cursor.fetchone()['count']
            data_checks.append(f"الإضافات: {plugins_count}")
            
            # فحص الأنشطة
            cursor.execute("SELECT COUNT(*) as count FROM activities")
            activities_count = cursor.fetchone()['count']
            data_checks.append(f"الأنشطة: {activities_count}")
            
            # التحقق من العلاقات الخارجية
            cursor.execute("""
            SELECT COUNT(*) as orphaned_analyses 
            FROM analyses a 
            LEFT JOIN projects p ON a.project_id = p.id 
            WHERE p.id IS NULL
            """)
            orphaned_analyses = cursor.fetchone()['orphaned_analyses']
            
            cursor.execute("""
            SELECT COUNT(*) as orphaned_errors 
            FROM errors e 
            LEFT JOIN projects p ON e.project_id = p.id 
            WHERE p.id IS NULL
            """)
            orphaned_errors = cursor.fetchone()['orphaned_errors']
            
            cursor.close()
            connection.close()
            
            issues = []
            if orphaned_analyses > 0:
                issues.append(f"تحليلات يتيمة: {orphaned_analyses}")
            if orphaned_errors > 0:
                issues.append(f"أخطاء يتيمة: {orphaned_errors}")
            
            duration = time.time() - start_time
            if issues:
                self.log_test("اختبار سلامة البيانات", "WARN", 
                            f"مشاكل في البيانات: {', '.join(issues)}", duration)
            else:
                self.log_test("اختبار سلامة البيانات", "PASS", 
                            f"البيانات سليمة - {', '.join(data_checks)}", duration)
            
            return len(issues) == 0
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار سلامة البيانات", "FAIL", f"خطأ: {e}", duration)
            self.errors_found.append(f"Data Integrity Error: {e}")
            return False
    
    def test_crud_operations(self) -> bool:
        """اختبار عمليات CRUD"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # اختبار إدراج مشروع جديد
            test_project_name = f"مشروع اختبار {datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute("""
            INSERT INTO projects (name, path, type, description)
            VALUES (%s, %s, %s, %s)
            """, (test_project_name, "/test/path", "test", "مشروع اختبار"))
            
            # الحصول على ID المشروع الجديد
            cursor.execute("SELECT LAST_INSERT_ID() as id")
            test_project_id = cursor.fetchone()['id']
            
            # اختبار قراءة المشروع
            cursor.execute("SELECT * FROM projects WHERE id = %s", (test_project_id,))
            project = cursor.fetchone()
            
            if not project or project['name'] != test_project_name:
                raise Exception("فشل في قراءة المشروع المدرج")
            
            # اختبار تحديث المشروع
            updated_description = "وصف محدث للاختبار"
            cursor.execute("""
            UPDATE projects SET description = %s WHERE id = %s
            """, (updated_description, test_project_id))
            
            # التحقق من التحديث
            cursor.execute("SELECT description FROM projects WHERE id = %s", (test_project_id,))
            updated_project = cursor.fetchone()
            
            if updated_project['description'] != updated_description:
                raise Exception("فشل في تحديث المشروع")
            
            # اختبار إدراج تحليل للمشروع
            cursor.execute("""
            INSERT INTO analyses (project_id, agent_type, analysis_data, results, score)
            VALUES (%s, %s, %s, %s, %s)
            """, (test_project_id, "test_agent", '{"test": true}', '{"result": "success"}', 95.5))
            
            # اختبار حذف المشروع (سيحذف التحليل تلقائياً بسبب CASCADE)
            cursor.execute("DELETE FROM projects WHERE id = %s", (test_project_id,))
            
            # التحقق من الحذف
            cursor.execute("SELECT COUNT(*) as count FROM projects WHERE id = %s", (test_project_id,))
            remaining_projects = cursor.fetchone()['count']
            
            cursor.execute("SELECT COUNT(*) as count FROM analyses WHERE project_id = %s", (test_project_id,))
            remaining_analyses = cursor.fetchone()['count']
            
            if remaining_projects > 0 or remaining_analyses > 0:
                raise Exception("فشل في حذف المشروع أو التحليلات المرتبطة")
            
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            self.log_test("اختبار عمليات CRUD", "PASS", 
                        "جميع عمليات الإنشاء والقراءة والتحديث والحذف نجحت", duration)
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار عمليات CRUD", "FAIL", f"خطأ: {e}", duration)
            self.errors_found.append(f"CRUD Error: {e}")
            return False
    
    def test_performance(self) -> bool:
        """اختبار الأداء"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # اختبار سرعة الاستعلامات
            performance_tests = []
            
            # اختبار 1: استعلام بسيط
            test_start = time.time()
            cursor.execute("SELECT COUNT(*) as count FROM projects")
            cursor.fetchone()
            simple_query_time = time.time() - test_start
            performance_tests.append(f"استعلام بسيط: {simple_query_time:.3f}s")
            
            # اختبار 2: استعلام معقد مع JOIN
            test_start = time.time()
            cursor.execute("""
            SELECT p.name, COUNT(a.id) as analyses_count, AVG(a.score) as avg_score
            FROM projects p
            LEFT JOIN analyses a ON p.id = a.project_id
            GROUP BY p.id, p.name
            ORDER BY avg_score DESC
            """)
            cursor.fetchall()
            complex_query_time = time.time() - test_start
            performance_tests.append(f"استعلام معقد: {complex_query_time:.3f}s")
            
            # اختبار 3: استعلام مع JSON
            test_start = time.time()
            cursor.execute("""
            SELECT * FROM analyses 
            WHERE JSON_EXTRACT(analysis_data, '$.files_analyzed') > 0
            """)
            cursor.fetchall()
            json_query_time = time.time() - test_start
            performance_tests.append(f"استعلام JSON: {json_query_time:.3f}s")
            
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            
            # تحديد حالة الاختبار بناءً على الأداء
            max_acceptable_time = 1.0  # ثانية واحدة كحد أقصى
            status = "PASS" if max(simple_query_time, complex_query_time, json_query_time) < max_acceptable_time else "WARN"
            
            self.log_test("اختبار الأداء", status, 
                        f"أوقات الاستعلامات: {', '.join(performance_tests)}", duration)
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار الأداء", "FAIL", f"خطأ: {e}", duration)
            self.errors_found.append(f"Performance Error: {e}")
            return False
    
    def test_security(self) -> bool:
        """اختبار الأمان الأساسي"""
        start_time = time.time()
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # اختبار حماية من SQL Injection (محاكاة)
            malicious_input = "'; DROP TABLE projects; --"
            cursor.execute("SELECT * FROM projects WHERE name = %s", (malicious_input,))
            cursor.fetchall()
            
            # التحقق من أن الجدول ما زال موجوداً
            cursor.execute("SHOW TABLES LIKE 'projects'")
            table_exists = cursor.fetchone()
            
            if not table_exists:
                raise Exception("فشل في الحماية من SQL Injection")
            
            cursor.close()
            connection.close()
            
            duration = time.time() - start_time
            self.log_test("اختبار الأمان", "PASS", 
                        "الحماية من SQL Injection تعمل بشكل صحيح", duration)
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_test("اختبار الأمان", "FAIL", f"خطأ: {e}", duration)
            self.errors_found.append(f"Security Error: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء الاختبار الشامل لقاعدة بيانات نظام أنوبيس")
        print("=" * 60)
        
        start_time = time.time()
        
        # تشغيل الاختبارات
        tests = [
            ("اختبار الاتصال", self.test_connection),
            ("اختبار هيكل قاعدة البيانات", self.test_database_structure),
            ("اختبار سلامة البيانات", self.test_data_integrity),
            ("اختبار عمليات CRUD", self.test_crud_operations),
            ("اختبار الأداء", self.test_performance),
            ("اختبار الأمان", self.test_security)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_function in tests:
            try:
                if test_function():
                    passed_tests += 1
            except Exception as e:
                self.log_test(test_name, "FAIL", f"خطأ غير متوقع: {e}")
                self.errors_found.append(f"Unexpected error in {test_name}: {e}")
        
        total_duration = time.time() - start_time
        
        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        status_icon = "🎉" if success_rate == 100 else "⚠️" if success_rate >= 80 else "❌"
        
        print(f"{status_icon} النتيجة الإجمالية: {passed_tests}/{total_tests} اختبار نجح ({success_rate:.1f}%)")
        print(f"⏱️ إجمالي وقت الاختبار: {total_duration:.3f} ثانية")
        
        if self.errors_found:
            print(f"\n❌ الأخطاء المكتشفة ({len(self.errors_found)}):")
            for i, error in enumerate(self.errors_found, 1):
                print(f"  {i}. {error}")
        
        # إنشاء تقرير مفصل
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'total_duration': total_duration,
            'test_results': self.test_results,
            'errors_found': self.errors_found,
            'database_config': {
                'host': self.config['host'],
                'port': self.config['port'],
                'database': self.config['database']
            }
        }
        
        return report


def main():
    """الدالة الرئيسية"""
    tester = DatabaseTester()
    report = tester.run_all_tests()
    
    # حفظ التقرير
    report_file = f"database/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 تم حفظ التقرير المفصل في: {report_file}")
    
    # تحديد كود الخروج
    exit_code = 0 if report['success_rate'] == 100 else 1
    return exit_code


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
