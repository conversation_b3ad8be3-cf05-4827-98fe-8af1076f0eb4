#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لواجهة التحكم
"""

import sys
import traceback

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import tkinter as tk
        print("✅ tkinter - OK")
    except ImportError as e:
        print(f"❌ tkinter - خطأ: {e}")
        return False
    
    try:
        from tkinter import ttk, messagebox, scrolledtext
        print("✅ tkinter modules - OK")
    except ImportError as e:
        print(f"❌ tkinter modules - خطأ: {e}")
        return False
    
    try:
        import psutil
        print("✅ psutil - OK")
    except ImportError as e:
        print(f"❌ psutil - خطأ: {e}")
        print("💡 تثبيت: pip install psutil")
        return False
    
    try:
        import subprocess
        import json
        import os
        import threading
        import time
        from datetime import datetime
        print("✅ المكتبات الأساسية - OK")
    except ImportError as e:
        print(f"❌ المكتبات الأساسية - خطأ: {e}")
        return False
    
    return True

def test_dashboard_creation():
    """اختبار إنشاء واجهة التحكم"""
    print("\n🎛️ اختبار إنشاء واجهة التحكم...")
    
    try:
        # استيراد الكلاس
        from process_control_dashboard import ProcessControlDashboard
        print("✅ استيراد ProcessControlDashboard - OK")
        
        # إنشاء نسخة (بدون تشغيل)
        print("🔄 إنشاء نسخة من التطبيق...")
        app = ProcessControlDashboard()
        print("✅ إنشاء التطبيق - OK")
        
        # اختبار بعض الوظائف
        print("🔄 اختبار الوظائف الأساسية...")
        app.log_message("اختبار السجل")
        print("✅ وظيفة السجل - OK")
        
        # إغلاق النافذة
        app.root.destroy()
        print("✅ إغلاق التطبيق - OK")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def test_vscode_detection():
    """اختبار اكتشاف VS Code"""
    print("\n🔍 اختبار اكتشاف VS Code...")
    
    try:
        import subprocess
        result = subprocess.run(['code', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print(f"✅ VS Code متاح: {version}")
            return True
        else:
            print("⚠️ VS Code غير متاح في PATH")
            print("💡 بعض المميزات قد لا تعمل")
            return False
    except Exception as e:
        print(f"⚠️ VS Code غير متاح: {e}")
        print("💡 بعض المميزات قد لا تعمل")
        return False

def test_process_monitoring():
    """اختبار مراقبة العمليات"""
    print("\n📊 اختبار مراقبة العمليات...")
    
    try:
        import psutil
        
        # البحث عن عمليات VS Code
        vscode_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                pinfo = proc.info
                if any(keyword in pinfo['name'].lower() for keyword in 
                      ['code', 'node', 'electron']):
                    vscode_processes.append(pinfo)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print(f"✅ تم العثور على {len(vscode_processes)} عملية مرتبطة بـ VS Code")
        
        if vscode_processes:
            print("📋 العمليات المكتشفة:")
            for proc in vscode_processes[:5]:  # أول 5 عمليات
                print(f"   - PID: {proc['pid']}, Name: {proc['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مراقبة العمليات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار واجهة التحكم في VS Code")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # اختبار استيراد المكتبات
    if test_imports():
        tests_passed += 1
    
    # اختبار إنشاء واجهة التحكم
    if test_dashboard_creation():
        tests_passed += 1
    
    # اختبار اكتشاف VS Code
    if test_vscode_detection():
        tests_passed += 1
    
    # اختبار مراقبة العمليات
    if test_process_monitoring():
        tests_passed += 1
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("\n🚀 للتشغيل:")
        print("   python process_control_dashboard.py")
        print("   أو")
        print("   run_dashboard.bat")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه")
        if tests_passed >= 2:
            print("💡 التطبيق قد يعمل مع قيود")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
