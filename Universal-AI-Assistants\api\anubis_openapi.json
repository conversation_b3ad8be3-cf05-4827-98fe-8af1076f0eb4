{"openapi": "3.1.0", "info": {"title": "Anubis AI System API", "description": "API for Anubis AI System - Multi-model AI collaboration platform", "version": "1.0.0", "contact": {"name": "Anubis AI System", "url": "https://github.com/anubis-ai-system"}}, "servers": [{"url": "http://localhost:8000", "description": "Local development server"}], "security": [{"ApiKeyAuth": []}], "paths": {"/api/v1/models/ollama/generate": {"post": {"tags": ["Ollama Models"], "summary": "Generate response using Ollama models", "description": "Generate AI response using local Ollama models (llama3:8b, mistral:7b, phi3:mini)", "operationId": "generate_ollama_response", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OllamaRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OllamaResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/models/gemini/generate": {"post": {"tags": ["Gemini Model"], "summary": "Generate response using Gemini", "description": "Generate AI response using Google Gemini model", "operationId": "generate_gemini_response", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeminiRequest"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeminiResponse"}}}}}}}, "/api/v1/agents/run": {"post": {"tags": ["Anubis Agents"], "summary": "<PERSON> agent", "description": "Execute specific Anubis agent for analysis", "operationId": "run_anubis_agent", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequest"}}}}, "responses": {"200": {"description": "Agent execution result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentResponse"}}}}}}}, "/api/v1/collaboration/analyze": {"post": {"tags": ["Collaboration"], "summary": "Collaborative analysis", "description": "Run collaborative analysis using multiple models and agents", "operationId": "collaborative_analysis", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollaborationRequest"}}}}, "responses": {"200": {"description": "Collaborative analysis result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CollaborationResponse"}}}}}}}, "/api/v1/database/projects": {"get": {"tags": ["Database"], "summary": "Get projects", "description": "Retrieve all projects from database", "operationId": "get_projects", "responses": {"200": {"description": "List of projects", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}}}}, "post": {"tags": ["Database"], "summary": "Create project", "description": "Create new project in database", "operationId": "create_project", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectCreate"}}}}, "responses": {"201": {"description": "Project created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}}}}, "/api/v1/langsmith/trace": {"post": {"tags": ["LangSmith Integration"], "summary": "Create Lang<PERSON><PERSON> trace", "description": "Create trace in LangSmith for monitoring", "operationId": "create_langsmith_trace", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LangSmithTraceRequest"}}}}, "responses": {"200": {"description": "Trace created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LangSmithTraceResponse"}}}}}}}}, "components": {"securitySchemes": {"ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-Key"}}, "schemas": {"OllamaRequest": {"type": "object", "required": ["prompt", "model"], "properties": {"prompt": {"type": "string", "description": "Input prompt for the model"}, "model": {"type": "string", "enum": ["llama3:8b", "mistral:7b", "phi3:mini"], "description": "Ollama model to use"}, "temperature": {"type": "number", "minimum": 0, "maximum": 2, "default": 0.7, "description": "Sampling temperature"}, "max_tokens": {"type": "integer", "minimum": 1, "maximum": 4096, "default": 1000, "description": "Maximum tokens to generate"}}}, "OllamaResponse": {"type": "object", "properties": {"model": {"type": "string", "description": "Model used"}, "prompt": {"type": "string", "description": "Input prompt"}, "response": {"type": "string", "description": "Generated response"}, "response_time": {"type": "number", "description": "Response time in seconds"}, "tokens_generated": {"type": "integer", "description": "Number of tokens generated"}, "status": {"type": "string", "enum": ["success", "error"], "description": "Response status"}}}, "GeminiRequest": {"type": "object", "required": ["prompt"], "properties": {"prompt": {"type": "string", "description": "Input prompt for Gemini"}, "temperature": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.7}}}, "GeminiResponse": {"type": "object", "properties": {"prompt": {"type": "string"}, "response": {"type": "string"}, "response_time": {"type": "number"}, "status": {"type": "string"}}}, "AgentRequest": {"type": "object", "required": ["agent_type", "project_path"], "properties": {"agent_type": {"type": "string", "enum": ["error_detector", "project_analyzer", "file_organizer", "memory_agent", "database_agent", "smart_ai_agent", "code_analyzer"], "description": "Type of agent to run"}, "project_path": {"type": "string", "description": "Path to project for analysis"}, "config": {"type": "object", "description": "Agent configuration"}, "verbose": {"type": "boolean", "default": true, "description": "Enable verbose output"}}}, "AgentResponse": {"type": "object", "properties": {"agent_type": {"type": "string"}, "project_path": {"type": "string"}, "result": {"type": "object", "description": "Agent analysis result"}, "execution_time": {"type": "number"}, "status": {"type": "string"}}}, "CollaborationRequest": {"type": "object", "required": ["task", "models", "agents"], "properties": {"task": {"type": "string", "description": "Task description"}, "models": {"type": "array", "items": {"type": "string"}, "description": "Models to use in collaboration"}, "agents": {"type": "array", "items": {"type": "string"}, "description": "Agents to use in collaboration"}, "project_path": {"type": "string", "description": "Project path for analysis"}, "collaboration_mode": {"type": "string", "enum": ["parallel", "sequential", "voting"], "default": "parallel", "description": "How models/agents should collaborate"}}}, "CollaborationResponse": {"type": "object", "properties": {"task": {"type": "string"}, "results": {"type": "object", "description": "Results from all models and agents"}, "consensus": {"type": "object", "description": "Consensus result"}, "execution_time": {"type": "number"}, "status": {"type": "string"}}}, "Project": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "path": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}, "ProjectCreate": {"type": "object", "required": ["name", "path", "type"], "properties": {"name": {"type": "string"}, "path": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}}}, "LangSmithTraceRequest": {"type": "object", "required": ["name", "inputs"], "properties": {"name": {"type": "string", "description": "Trace name"}, "inputs": {"type": "object", "description": "Trace inputs"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Trace tags"}}}, "LangSmithTraceResponse": {"type": "object", "properties": {"trace_id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}}}, "HTTPValidationError": {"type": "object", "properties": {"detail": {"type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}}}, "ValidationError": {"type": "object", "properties": {"loc": {"type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}}, "msg": {"type": "string"}, "type": {"type": "string"}}}}}}