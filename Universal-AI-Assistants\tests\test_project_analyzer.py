#!/usr/bin/env python3
"""
🧪 اختبارات وكيل تحليل المشاريع
Project Analyzer Agent Tests

اختبارات شاملة لوكيل تحليل المشاريع الذكي
"""

import os
import sys
import unittest
import tempfile
import shutil
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'agents'))

try:
    from agents.project_analyzer_agent import ProjectAnalyzerAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد ProjectAnalyzerAgent: {e}")
    sys.exit(1)

class TestProjectAnalyzerAgent(unittest.TestCase):
    """اختبارات وكيل تحليل المشاريع"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء مجلد مؤقت للاختبار
        self.test_dir = tempfile.mkdtemp()
        self.test_project = Path(self.test_dir)
        
        # إعدادات الوكيل
        self.config = {
            'deep_analysis': True,
            'performance_check': True,
            'security_scan': True
        }
        
        # إنشاء الوكيل
        self.agent = ProjectAnalyzerAgent(
            project_path=str(self.test_project),
            config=self.config,
            verbose=False
        )
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        # حذف المجلد المؤقت
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_agent_initialization(self):
        """اختبار تهيئة الوكيل"""
        self.assertEqual(self.agent.get_agent_type(), "project_analyzer")
        self.assertTrue(self.agent.is_initialized)
        self.assertEqual(self.agent.status, "initialized")
        self.assertTrue(self.agent.deep_analysis)
        self.assertTrue(self.agent.performance_check)
        self.assertTrue(self.agent.security_scan)
    
    def test_project_size_analysis(self):
        """اختبار تحليل حجم المشروع"""
        # إنشاء ملفات متنوعة
        files_data = {
            "main.py": "print('Hello World')\n" * 10,
            "utils.py": "def helper():\n    pass\n" * 20,
            "config.json": '{"setting": "value"}',
            "README.md": "# Project Title\n\nDescription",
            "data.csv": "name,age\nJohn,25\nJane,30"
        }
        
        for filename, content in files_data.items():
            test_file = self.test_project / filename
            test_file.write_text(content)
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('project_size_analysis', result)
        
        size_analysis = result['project_size_analysis']
        self.assertEqual(size_analysis['total_files'], 5)
        self.assertGreater(size_analysis['total_lines'], 0)
        self.assertGreater(size_analysis['code_lines'], 0)
        
        # التحقق من تصنيف أنواع الملفات
        file_types = size_analysis['file_types']
        self.assertIn('.py', file_types)
        self.assertIn('.json', file_types)
        self.assertIn('.md', file_types)
        self.assertIn('.csv', file_types)
    
    def test_complexity_analysis(self):
        """اختبار تحليل التعقيد"""
        # إنشاء ملف بتعقيد متوسط
        test_file = self.test_project / "complex_code.py"
        test_file.write_text("""
def complex_function(x, y):
    if x > 0:
        if y > 0:
            for i in range(x):
                if i % 2 == 0:
                    try:
                        result = x / y
                    except ZeroDivisionError:
                        result = 0
                    else:
                        result += 1
                elif i % 3 == 0:
                    result = x * y
                else:
                    result = x - y
            return result
        else:
            return -1
    else:
        return 0
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('complexity_analysis', result)
        
        complexity_analysis = result['complexity_analysis']
        self.assertGreater(len(complexity_analysis['module_complexity']), 0)
        self.assertIn('average_complexity', complexity_analysis)
        self.assertIn('complexity_distribution', complexity_analysis)
    
    def test_dependency_analysis(self):
        """اختبار تحليل التبعيات"""
        # إنشاء ملف requirements.txt
        req_file = self.test_project / "requirements.txt"
        req_file.write_text("""
numpy>=1.20.0
pandas>=1.3.0
requests>=2.25.0
# تعليق
flask>=2.0.0
""")
        
        # إنشاء ملف Python باستيرادات
        py_file = self.test_project / "imports.py"
        py_file.write_text("""
import os
import sys
import json
from datetime import datetime
import numpy as np
import pandas as pd
import requests
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('dependency_analysis', result)
        
        dependency_analysis = result['dependency_analysis']
        self.assertGreater(dependency_analysis['total_dependencies'], 0)
        self.assertGreater(len(dependency_analysis['external_dependencies']), 0)
        self.assertGreater(len(dependency_analysis['internal_dependencies']), 0)
    
    def test_architecture_analysis(self):
        """اختبار تحليل الهيكل المعماري"""
        # إنشاء هيكل مشروع منظم
        (self.test_project / "src").mkdir()
        (self.test_project / "tests").mkdir()
        (self.test_project / "docs").mkdir()
        
        # إنشاء ملفات في الهيكل
        (self.test_project / "src" / "main.py").write_text("print('main')")
        (self.test_project / "src" / "models.py").write_text("class Model: pass")
        (self.test_project / "tests" / "test_main.py").write_text("def test(): pass")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('architecture_analysis', result)
        
        architecture_analysis = result['architecture_analysis']
        self.assertIn('project_type', architecture_analysis)
        self.assertIn('directory_structure', architecture_analysis)
        self.assertIn('architectural_style', architecture_analysis)
        self.assertIn('modularity_score', architecture_analysis)
        
        # التحقق من نقاط التنظيم
        dir_structure = architecture_analysis['directory_structure']
        self.assertGreater(dir_structure['organization_score'], 0)
    
    def test_performance_analysis(self):
        """اختبار تحليل الأداء"""
        # إنشاء ملف بمشاكل أداء محتملة
        test_file = self.test_project / "performance_issues.py"
        test_file.write_text("""
import time

def slow_function():
    for i in range(1000):
        for j in range(1000):
            time.sleep(0.001)  # محاكاة عملية بطيئة
    return "done"

def another_slow_function():
    time.sleep(5)  # انتظار طويل
    return "finished"
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('performance_analysis', result)
        
        performance_analysis = result['performance_analysis']
        self.assertIn('potential_bottlenecks', performance_analysis)
        
        # قد تكون هناك مشاكل أداء مكتشفة
        if performance_analysis['potential_bottlenecks']:
            bottleneck = performance_analysis['potential_bottlenecks'][0]
            self.assertIn('file', bottleneck)
            self.assertIn('issue', bottleneck)
            self.assertIn('severity', bottleneck)
    
    def test_security_analysis(self):
        """اختبار تحليل الأمان"""
        # إنشاء ملف بمشاكل أمنية
        test_file = self.test_project / "security_issues.py"
        test_file.write_text("""
# مشاكل أمنية خطيرة
user_input = input("Enter command: ")
eval(user_input)  # خطر أمني عالي

password = "secret123"  # كلمة مرور مكشوفة

def dangerous_function(code):
    exec(code)  # خطر أمني عالي
    return "executed"
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('security_analysis', result)
        
        security_analysis = result['security_analysis']
        self.assertIn('security_issues', security_analysis)
        self.assertIn('security_score', security_analysis)
        
        # يجب أن تكون هناك مشاكل أمنية مكتشفة
        self.assertGreater(len(security_analysis['security_issues']), 0)
        
        # التحقق من نوع المشاكل الأمنية
        security_issue = security_analysis['security_issues'][0]
        self.assertIn('file', security_issue)
        self.assertIn('issue', security_issue)
        self.assertIn('severity', security_issue)
    
    def test_maintainability_analysis(self):
        """اختبار تحليل قابلية الصيانة"""
        # إنشاء ملفات بتوثيق متفاوت
        well_documented = self.test_project / "well_documented.py"
        well_documented.write_text("""
def documented_function():
    \"\"\"
    دالة موثقة جيداً
    \"\"\"
    return "documented"

class DocumentedClass:
    \"\"\"
    فئة موثقة جيداً
    \"\"\"
    
    def method(self):
        \"\"\"
        طريقة موثقة
        \"\"\"
        pass
""")
        
        poorly_documented = self.test_project / "poorly_documented.py"
        poorly_documented.write_text("""
def undocumented_function():
    return "undocumented"

class UndocumentedClass:
    def method(self):
        pass
""")
        
        # إنشاء ملف اختبار
        test_file = self.test_project / "test_example.py"
        test_file.write_text("""
def test_something():
    assert True
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        self.assertIn('maintainability_analysis', result)
        
        maintainability_analysis = result['maintainability_analysis']
        self.assertIn('maintainability_index', maintainability_analysis)
        self.assertIn('documentation_coverage', maintainability_analysis)
        self.assertIn('test_coverage_estimate', maintainability_analysis)
        
        # يجب أن تكون هناك تغطية توثيق جزئية
        self.assertGreater(maintainability_analysis['documentation_coverage'], 0)
        self.assertGreater(maintainability_analysis['test_coverage_estimate'], 0)
    
    def test_recommendations_generation(self):
        """اختبار إنشاء التوصيات"""
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من وجود التوصيات
        self.assertTrue(result['success'])
        self.assertIn('recommendations', result)
        
        recommendations = result['recommendations']
        self.assertGreater(len(recommendations), 0)
        
        # التحقق من بنية التوصية
        recommendation = recommendations[0]
        self.assertIn('category', recommendation)
        self.assertIn('priority', recommendation)
        self.assertIn('title', recommendation)
        self.assertIn('description', recommendation)
        self.assertIn('action', recommendation)
    
    def test_summary_creation(self):
        """اختبار إنشاء الملخص"""
        # إنشاء مشروع بسيط
        test_file = self.test_project / "simple.py"
        test_file.write_text("""
def hello():
    return "Hello World"
""")
        
        # تشغيل التحليل
        result = self.agent.run()
        
        # التحقق من الملخص
        self.assertTrue(result['success'])
        self.assertIn('summary', result)
        
        summary = result['summary']
        self.assertIn('overall_score', summary)
        self.assertIn('project_size', summary)
        self.assertIn('code_lines', summary)
        self.assertIn('complexity_level', summary)
        self.assertIn('security_status', summary)
        self.assertIn('maintainability_level', summary)
        self.assertIn('recommendations_count', summary)
        self.assertIn('analysis_time', summary)
        
        # التحقق من القيم المنطقية
        self.assertGreaterEqual(summary['overall_score'], 0)
        self.assertLessEqual(summary['overall_score'], 100)
    
    def test_empty_project_analysis(self):
        """اختبار تحليل مشروع فارغ"""
        # تشغيل التحليل على مشروع فارغ
        result = self.agent.run()
        
        # التحقق من النتائج
        self.assertTrue(result['success'])
        
        # التحقق من أن التحليل يتعامل مع المشروع الفارغ بشكل صحيح
        size_analysis = result['project_size_analysis']
        self.assertEqual(size_analysis['total_files'], 0)
        self.assertEqual(size_analysis['code_lines'], 0)

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبارات وكيل تحليل المشاريع...")
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestProjectAnalyzerAgent)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # عرض النتائج
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        return True
    else:
        print(f"❌ فشل {len(result.failures)} اختبار")
        print(f"⚠️ خطأ في {len(result.errors)} اختبار")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
