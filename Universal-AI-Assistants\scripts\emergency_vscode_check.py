#!/usr/bin/env python3
"""
Emergency VS Code Check
فحص طوارئ لـ VS Code

Ultra-fast emergency check specifically for high process count situations (like 72+ processes).
"""

import psutil
import time

def emergency_scan():
    """فحص طوارئ سريع"""
    print("🚨 EMERGENCY VS CODE SCAN")
    print("=" * 30)
    
    vscode_count = 0
    total_memory_mb = 0
    high_memory_processes = []
    
    print("🔍 Scanning processes...")
    
    for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
        try:
            name = proc.info['name'].lower()
            if 'code' in name:
                memory_mb = proc.info['memory_info'].rss / (1024 * 1024)
                vscode_count += 1
                total_memory_mb += memory_mb
                
                # تسجيل العمليات عالية الاستهلاك
                if memory_mb > 200:  # أكثر من 200 MB
                    high_memory_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': memory_mb
                    })
        except:
            continue
    
    return vscode_count, total_memory_mb, high_memory_processes

def show_emergency_status(count, memory_mb, high_memory_procs):
    """عرض حالة الطوارئ"""
    memory_gb = memory_mb / 1024
    
    print(f"\n📊 EMERGENCY STATUS:")
    print(f"   VS Code Processes: {count}")
    print(f"   Total Memory: {memory_gb:.1f} GB")
    print(f"   High Memory Processes: {len(high_memory_procs)}")
    
    # تحديد مستوى الخطر
    if count >= 72:
        danger_level = "🔴 CRITICAL"
        print(f"\n{danger_level}")
        print("⚠️  EXACTLY YOUR SITUATION: 72+ processes!")
        print("This is an emergency situation requiring immediate action!")
        
    elif count > 50:
        danger_level = "🟠 HIGH DANGER"
        print(f"\n{danger_level}")
        print("Very high process count - action needed soon!")
        
    elif count > 30:
        danger_level = "🟡 MODERATE"
        print(f"\n{danger_level}")
        print("Elevated process count - monitoring needed")
        
    else:
        danger_level = "🟢 NORMAL"
        print(f"\n{danger_level}")
        print("Process count is within normal range")
    
    return danger_level

def show_immediate_actions(count, memory_gb):
    """عرض الإجراءات الفورية"""
    print(f"\n🚨 IMMEDIATE ACTIONS REQUIRED:")
    
    if count >= 70:
        print("1. 💾 SAVE ALL YOUR WORK RIGHT NOW!")
        print("2. 🔴 Close VS Code completely (all windows)")
        print("3. ⏳ Wait 30 seconds for processes to terminate")
        print("4. ⚡ Restart VS Code with --disable-extensions")
        print("5. 📊 Open only ONE workspace")
        print("6. 🔧 Enable only essential extensions")
        
        print(f"\n⚠️  DO NOT:")
        print("• Open multiple VS Code windows")
        print("• Install new extensions")
        print("• Open large workspaces")
        print("• Ignore this warning")
        
    elif count > 50:
        print("1. 💾 Save your current work")
        print("2. 🔄 Close unnecessary VS Code windows")
        print("3. 🔧 Disable non-essential extensions")
        print("4. 📊 Consider restarting VS Code")
        
    elif count > 30:
        print("1. 👀 Monitor the situation")
        print("2. 🔄 Close unused windows when convenient")
        print("3. 🔧 Review active extensions")

def show_emergency_commands():
    """عرض أوامر الطوارئ"""
    print(f"\n🛠️  EMERGENCY COMMANDS:")
    print("Quick cleanup:")
    print("   python scripts/vscode_emergency_cleanup.py")
    print()
    print("Detailed analysis:")
    print("   python scripts/vscode_heavy_load_analyzer.py")
    print()
    print("Emergency guide:")
    print("   docs/vscode_72_processes_emergency_guide.md")
    print()
    print("Restart VS Code safely:")
    print("   code --disable-extensions")

def main():
    """الدالة الرئيسية"""
    start_time = time.time()
    
    try:
        # فحص طوارئ
        count, memory_mb, high_memory_procs = emergency_scan()
        
        if count == 0:
            print("✅ No VS Code processes found - system is clean!")
            return
        
        # عرض الحالة
        danger_level = show_emergency_status(count, memory_mb, high_memory_procs)
        
        # عرض العمليات عالية الاستهلاك
        if high_memory_procs:
            print(f"\n🔥 HIGH MEMORY PROCESSES:")
            for proc in high_memory_procs[:5]:  # أعلى 5
                print(f"   PID {proc['pid']}: {proc['memory_mb']:.1f} MB - {proc['name']}")
        
        # عرض الإجراءات الفورية
        show_immediate_actions(count, memory_mb / 1024)
        
        # عرض أوامر الطوارئ
        if count > 30:
            show_emergency_commands()
        
        scan_time = time.time() - start_time
        print(f"\n⏱️  Emergency scan completed in {scan_time:.1f}s")
        
        # رسالة خاصة للحالة الحرجة
        if count >= 70:
            print(f"\n" + "🚨" * 20)
            print("CRITICAL: Your VS Code has 70+ processes!")
            print("This matches the 72-process emergency situation!")
            print("Follow the immediate actions above RIGHT NOW!")
            print("🚨" * 20)
        
    except Exception as e:
        print(f"❌ Emergency scan failed: {e}")
        print("Try manually closing VS Code windows")

if __name__ == "__main__":
    main()
