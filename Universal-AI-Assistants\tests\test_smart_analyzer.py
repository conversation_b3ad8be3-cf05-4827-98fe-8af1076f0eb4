#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار محلل الكود الذكي
Test Smart Code Analyzer

اختبار شامل للوكيل الذكي الجديد
"""

import os
import sys
import json
from datetime import datetime

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

try:
    from agents.smart_code_analyzer import SmartCodeAnalyzer
except ImportError as e:
    print(f"❌ خطأ في استيراد محلل الكود الذكي: {e}")
    sys.exit(1)


def test_smart_analyzer():
    """اختبار محلل الكود الذكي"""
    print("🧠 اختبار محلل الكود الذكي المدعوم بالذكاء الاصطناعي")
    print("=" * 60)
    
    # إنشاء محلل الكود الذكي
    analyzer = SmartCodeAnalyzer(
        project_path=".",
        config={'test_mode': True},
        verbose=True
    )
    
    print(f"✅ تم إنشاء المحلل - الذكاء الاصطناعي: {'مفعل' if analyzer.is_ai_enabled() else 'غير مفعل'}")
    
    # اختبار تحليل ملف واحد
    print("\n🔍 اختبار تحليل ملف واحد...")
    
    # اختيار ملف للاختبار
    test_file = "quick_ai_test.py"  # الملف الذي أنشأناه
    
    if os.path.exists(test_file):
        print(f"📄 تحليل الملف: {test_file}")
        
        result = analyzer.analyze_file_with_ai(test_file)
        
        if result['status'] == 'success':
            print("✅ نجح التحليل!")
            
            # عرض معلومات الملف
            file_info = result['file_info']
            print(f"   📊 معلومات الملف:")
            print(f"      - الاسم: {file_info['name']}")
            print(f"      - الحجم: {file_info['size']} حرف")
            print(f"      - الأسطر: {file_info['lines']}")
            print(f"      - اللغة: {file_info['language']}")
            
            # عرض التحليل التقليدي
            traditional = result['traditional_analysis']
            print(f"   🔧 التحليل التقليدي:")
            print(f"      - أسطر الكود: {traditional['basic_metrics']['code_lines']}")
            print(f"      - أسطر التعليقات: {traditional['basic_metrics']['comment_lines']}")
            print(f"      - عدد الدوال: {traditional['complexity_indicators']['function_count']}")
            print(f"      - المشاكل المكتشفة: {len(traditional['potential_issues'])}")
            
            # عرض التحليل الذكي
            if result['ai_analysis'] and not result['ai_analysis'].get('error'):
                ai_analysis = result['ai_analysis']
                print(f"   🧠 التحليل الذكي:")
                print(f"      - تقييم الجودة: {ai_analysis.get('quality_score', 'غير محدد')}/10")
                print(f"      - طول التحليل: {ai_analysis.get('analysis_length', 0)} حرف")
                
                # عرض جزء من التحليل الذكي
                if ai_analysis.get('ai_response'):
                    print(f"   📝 ملخص التحليل الذكي:")
                    print(f"      {ai_analysis['ai_response'][:200]}...")
            else:
                print(f"   ⚠️ التحليل الذكي: {result['ai_analysis'].get('error', 'غير متاح')}")
        else:
            print(f"❌ فشل التحليل: {result['message']}")
    else:
        print(f"❌ الملف غير موجود: {test_file}")
    
    # اختبار تحليل ملفات المشروع
    print("\n📁 اختبار تحليل ملفات المشروع...")
    
    project_analysis = analyzer.analyze_project_files(max_files=5)
    
    if project_analysis['status'] == 'completed':
        print(f"✅ تم تحليل {project_analysis['files_analyzed']} ملف")
        
        summary = project_analysis['summary']
        print(f"   📊 ملخص المشروع:")
        print(f"      - إجمالي الملفات: {summary['total_files']}")
        print(f"      - إجمالي الأسطر: {summary['total_lines']}")
        print(f"      - متوسط الجودة: {summary.get('average_quality_score', 'غير محدد')}")
        print(f"      - المشاكل المكتشفة: {summary['total_issues_found']}")
        print(f"      - اللغات: {', '.join(summary['languages_detected'])}")
        
        # عرض بعض المشاكل الشائعة
        if summary['common_issues']:
            print(f"   ⚠️ المشاكل الشائعة:")
            for issue in summary['common_issues'][:3]:
                print(f"      - {issue}")
    else:
        print("❌ فشل في تحليل ملفات المشروع")
    
    # اختبار التوصيات الذكية
    print("\n💡 اختبار التوصيات الذكية...")
    
    recommendations = analyzer.get_smart_recommendations()
    
    if recommendations:
        print(f"✅ تم الحصول على {len(recommendations)} توصية:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"   {i}. {rec}")
    else:
        print("❌ لم يتم الحصول على توصيات")
    
    # إحصائيات الاستخدام
    print("\n📈 إحصائيات الاستخدام:")
    print(f"   - الملفات المحللة: {len(analyzer.analysis_results)}")
    print(f"   - الذكاء الاصطناعي: {'✅ مفعل' if analyzer.is_ai_enabled() else '❌ غير مفعل'}")
    
    return analyzer


def create_test_report(analyzer):
    """إنشاء تقرير اختبار"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_type': 'smart_code_analyzer_test',
        'analyzer_info': {
            'agent_type': analyzer.agent_type,
            'ai_enabled': analyzer.is_ai_enabled(),
            'supported_languages': list(analyzer.supported_extensions.values())
        },
        'test_results': {
            'files_analyzed': len(analyzer.analysis_results),
            'analysis_results': analyzer.analysis_results
        },
        'ai_status': analyzer.get_ai_status()
    }
    
    # حفظ التقرير
    report_file = f"smart_analyzer_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الاختبار في: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")
    
    return report


def main():
    """الدالة الرئيسية"""
    try:
        # تشغيل الاختبار
        analyzer = test_smart_analyzer()
        
        # إنشاء التقرير
        report = create_test_report(analyzer)
        
        # النتيجة النهائية
        print("\n" + "=" * 60)
        print("🏆 نتائج اختبار محلل الكود الذكي:")
        
        if analyzer.is_ai_enabled() and len(analyzer.analysis_results) > 0:
            print("✅ المحلل الذكي يعمل بكامل طاقته!")
            print("🧠 الذكاء الاصطناعي مفعل ويقدم تحليلات متقدمة")
            return 0
        elif len(analyzer.analysis_results) > 0:
            print("⚠️ المحلل يعمل بالوضع التقليدي (بدون ذكاء اصطناعي)")
            return 1
        else:
            print("❌ المحلل لا يعمل بشكل صحيح")
            return 2
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المحلل الذكي: {e}")
        return 3


if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏺 انتهى اختبار محلل الكود الذكي - كود الخروج: {exit_code}")
    sys.exit(exit_code)
