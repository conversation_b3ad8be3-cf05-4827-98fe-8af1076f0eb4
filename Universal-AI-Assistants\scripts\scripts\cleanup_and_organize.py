#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 منظم وتنظيف شامل لمشروع نظام أنوبيس
Anubis Project Comprehensive Cleanup and Organizer

تنظيف شامل وتنظيم الملفات وحذف غير المستخدم وتحديث التوثيق
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
import glob


class AnubisProjectCleaner:
    """🧹 منظف ومنظم مشروع أنوبيس الشامل"""
    
    def __init__(self, project_root: str = "."):
        """تهيئة المنظف"""
        self.project_root = Path(project_root).resolve()
        self.archive_dir = self.project_root / "archive"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.cleanup_log = []
        
    def log_action(self, action: str, details: str = ""):
        """تسجيل العمليات"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'details': details
        }
        self.cleanup_log.append(log_entry)
        print(f"📝 {action}: {details}")
    
    def clean_duplicate_reports(self):
        """تنظيف التقارير المكررة"""
        print("🧹 تنظيف التقارير المكررة...")
        
        database_dir = self.project_root / "database"
        if not database_dir.exists():
            return
        
        # أنماط التقارير المكررة
        report_patterns = [
            "final_validation_report_*.json",
            "final_validation_report_*.html", 
            "simple_validation_report_*.json",
            "all_tests_report_*.html",
            "stress_test_report_*.json"
        ]
        
        archived_count = 0
        for pattern in report_patterns:
            report_files = list(database_dir.glob(pattern))
            
            if len(report_files) > 1:
                # ترتيب الملفات حسب تاريخ التعديل (الأحدث أولاً)
                report_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                
                # الاحتفاظ بأحدث ملف وأرشفة الباقي
                for old_file in report_files[1:]:
                    archive_path = self.archive_dir / "duplicate_reports" / old_file.name
                    archive_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.move(str(old_file), str(archive_path))
                    archived_count += 1
                    self.log_action("أرشفة تقرير مكرر", old_file.name)
        
        self.log_action("تنظيف التقارير المكررة", f"تم أرشفة {archived_count} ملف")
    
    def clean_backup_files(self):
        """تنظيف ملفات النسخ الاحتياطية القديمة"""
        print("🧹 تنظيف ملفات النسخ الاحتياطية...")
        
        backup_patterns = [
            "*.backup_*.py",
            "*.bak",
            "*~"
        ]
        
        archived_count = 0
        for pattern in backup_patterns:
            backup_files = list(self.project_root.rglob(pattern))
            
            for backup_file in backup_files:
                # تجاهل ملفات الأرشيف
                if self.archive_dir in backup_file.parents:
                    continue
                
                archive_path = self.archive_dir / "old_files" / backup_file.name
                archive_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(backup_file), str(archive_path))
                archived_count += 1
                self.log_action("أرشفة نسخة احتياطية", backup_file.name)
        
        self.log_action("تنظيف النسخ الاحتياطية", f"تم أرشفة {archived_count} ملف")
    
    def clean_unused_scripts(self):
        """تنظيف السكريبتات غير المستخدمة"""
        print("🧹 تنظيف السكريبتات غير المستخدمة...")
        
        # السكريبتات التي يمكن أرشفتها
        unused_scripts = [
            "anubis_auto_fix.py",
            "anubis_error_fix.py", 
            "organize_project.py"
        ]
        
        archived_count = 0
        for script_name in unused_scripts:
            script_path = self.project_root / script_name
            if script_path.exists():
                archive_path = self.archive_dir / "unused_files" / script_name
                archive_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(script_path), str(archive_path))
                archived_count += 1
                self.log_action("أرشفة سكريبت غير مستخدم", script_name)
        
        self.log_action("تنظيف السكريبتات", f"تم أرشفة {archived_count} ملف")
    
    def clean_redundant_docs(self):
        """تنظيف الوثائق المكررة"""
        print("🧹 تنظيف الوثائق المكررة...")
        
        database_dir = self.project_root / "database"
        redundant_docs = [
            "FINAL_VALIDATION_FIXES.md",
            "FILE_SPLIT_REPORT.md", 
            "README_SPLIT.md"
        ]
        
        archived_count = 0
        for doc_name in redundant_docs:
            doc_path = database_dir / doc_name
            if doc_path.exists():
                archive_path = self.archive_dir / "old_files" / doc_name
                archive_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(doc_path), str(archive_path))
                archived_count += 1
                self.log_action("أرشفة وثيقة مكررة", doc_name)
        
        self.log_action("تنظيف الوثائق المكررة", f"تم أرشفة {archived_count} ملف")
    
    def clean_project_root_docs(self):
        """تنظيف الوثائق في الجذر"""
        print("🧹 تنظيف وثائق الجذر...")
        
        root_docs_to_archive = [
            "ANUBIS_ERROR_CORRECTION_REPORT.md",
            "ORGANIZATION_COMPLETE.md"
        ]
        
        archived_count = 0
        for doc_name in root_docs_to_archive:
            doc_path = self.project_root / doc_name
            if doc_path.exists():
                archive_path = self.archive_dir / "old_files" / doc_name
                archive_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(doc_path), str(archive_path))
                archived_count += 1
                self.log_action("أرشفة وثيقة جذر", doc_name)
        
        self.log_action("تنظيف وثائق الجذر", f"تم أرشفة {archived_count} ملف")
    
    def clean_pycache(self):
        """تنظيف ملفات __pycache__"""
        print("🧹 تنظيف ملفات __pycache__...")
        
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
                self.log_action("حذف __pycache__", str(pycache_dir.relative_to(self.project_root)))
            except Exception as e:
                self.log_action("خطأ في حذف __pycache__", f"{pycache_dir}: {e}")
    
    def organize_database_files(self):
        """تنظيم ملفات قاعدة البيانات"""
        print("📁 تنظيم ملفات قاعدة البيانات...")
        
        database_dir = self.project_root / "database"
        
        # إنشاء مجلدات فرعية
        subdirs = {
            "tests": ["*test*.py", "run_all_tests.py"],
            "setup": ["direct_setup.py", "setup_database.py", "create_mysql_database.sql"],
            "core": ["database_validator.py", "final_validation_runner.py", "mysql_connector.py", "mysql_manager.py"],
            "docs": ["README.md", "TEST_SUMMARY.md", "FIXES_SUMMARY.md"]
        }
        
        moved_count = 0
        for subdir, patterns in subdirs.items():
            subdir_path = database_dir / subdir
            subdir_path.mkdir(exist_ok=True)
            
            for pattern in patterns:
                files = list(database_dir.glob(pattern))
                for file_path in files:
                    if file_path.is_file() and file_path.parent == database_dir:
                        new_path = subdir_path / file_path.name
                        if not new_path.exists():
                            shutil.move(str(file_path), str(new_path))
                            moved_count += 1
                            self.log_action("نقل ملف قاعدة البيانات", f"{file_path.name} -> {subdir}/")
        
        self.log_action("تنظيم ملفات قاعدة البيانات", f"تم نقل {moved_count} ملف")
    
    def update_main_readme(self):
        """تحديث ملف README الرئيسي"""
        print("📝 تحديث ملف README الرئيسي...")
        
        readme_content = """# 🏺 نظام أنوبيس للمساعدين الذكيين
# Anubis Universal AI Assistants System

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://mysql.com)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Tests](https://img.shields.io/badge/Tests-100%25%20Pass-brightgreen.svg)](database/docs/TEST_SUMMARY.md)
[![Version](https://img.shields.io/badge/Version-2.0.0-success.svg)](CHANGELOG.md)

نظام شامل للمساعدين الذكيين متعددي الأغراض مع قاعدة بيانات متقدمة ونظام إضافات مرن.

## 🌟 المميزات الرئيسية

- 🤖 **وكلاء ذكيون متخصصون** - مجموعة من الوكلاء المتخصصين لمهام مختلفة
- 🗄️ **قاعدة بيانات متقدمة** - نظام قاعدة بيانات MySQL مع إدارة شاملة
- 🔌 **نظام إضافات مرن** - إمكانية إضافة وظائف جديدة بسهولة
- 📊 **تحليل المشاريع** - تحليل شامل للمشاريع البرمجية
- 🔍 **كشف الأخطاء** - نظام ذكي لكشف وتصحيح الأخطاء
- 📁 **تنظيم الملفات** - تنظيم تلقائي للملفات والمجلدات
- 🧠 **نظام الذاكرة** - حفظ واسترجاع المعلومات بذكاء
- ✅ **اختبارات شاملة** - نظام اختبار متكامل مع تقارير مفصلة

## 🚀 البدء السريع

### المتطلبات
- Python 3.8+
- MySQL 8.0+
- pip

### التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/your-username/Universal-AI-Assistants.git
cd Universal-AI-Assistants

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\\Scripts\\activate     # على Windows

# تثبيت المتطلبات
pip install -r requirements.txt
pip install -r requirements_database.txt

# إعداد قاعدة البيانات
python database/setup/direct_setup.py

# تشغيل النظام
python main.py
```

### إعداد قاعدة البيانات

```bash
# إعداد سريع
python database/setup/direct_setup.py

# اختبار الاتصال
python database/tests/test_connection.py

# تشغيل جميع الاختبارات
python database/tests/run_all_tests.py
```

## 📁 هيكل المشروع

```
Universal-AI-Assistants/
├── 📋 README.md                 # دليل المشروع الرئيسي
├── 🐍 main.py                   # نقطة البداية الرئيسية
├── 📦 requirements.txt          # متطلبات Python الأساسية
├── 📦 requirements_database.txt # متطلبات قاعدة البيانات
├── 🚫 .gitignore               # ملفات مستبعدة من Git
├── 📋 PROJECT_STRUCTURE.md     # توثيق هيكل المشروع
│
├── 🧠 core/                     # النواة الأساسية للنظام
├── 🤖 agents/                   # الوكلاء الذكيون
├── 🗄️ database/                # قاعدة البيانات والاختبارات
│   ├── 🔧 core/                # ملفات قاعدة البيانات الأساسية
│   ├── 🧪 tests/               # اختبارات قاعدة البيانات
│   ├── ⚙️ setup/               # ملفات الإعداد
│   └── 📚 docs/                # توثيق قاعدة البيانات
├── ⚙️ configs/                 # ملفات الإعدادات
├── 🔌 plugins/                 # النظام الإضافي
├── 🧪 tests/                   # اختبارات النظام
├── 📚 docs/                    # التوثيق
├── 📜 scripts/                 # سكريبتات مساعدة
├── 📄 templates/               # قوالب المشاريع
├── 💼 workspace/               # مساحة العمل
└── 📦 archive/                 # الأرشيف (مستبعد من Git)
```

## 🧪 الاختبارات

### اختبارات قاعدة البيانات
```bash
# اختبار الاتصال الأساسي
python database/tests/test_connection.py

# التحقق المبسط
python database/tests/simple_validation.py

# الاختبار الشامل
python database/tests/comprehensive_test.py

# اختبار الضغط
python database/tests/stress_test.py

# تشغيل جميع اختبارات قاعدة البيانات
python database/tests/run_all_tests.py
```

### اختبارات النظام
```bash
# تشغيل جميع اختبارات النظام
python tests/run_all_tests.py

# اختبار تفاعلي
python tests/ask_anubis.py

# اختبار النظام الشامل
python tests/test_anubis_system.py
```

## 📊 حالة النظام

### قاعدة البيانات
- ✅ **MySQL 8.0.42** - يعمل بشكل مثالي
- ✅ **6 جداول** - جميعها منشأة ومختبرة
- ✅ **42+ سجل** - بيانات تجريبية جاهزة
- ✅ **100% نجاح** في جميع الاختبارات
- ✅ **أداء ممتاز** - متوسط وقت الاستعلام 0.001s

### الاختبارات
- ✅ **5/5 اختبارات** نجحت بنسبة 100%
- ✅ **اختبار الاتصال** - نجح
- ✅ **التحقق المبسط** - نجح
- ✅ **الاختبار الشامل** - نجح
- ✅ **اختبار الضغط** - نجح
- ✅ **مدير قاعدة البيانات** - نجح

## 🛠️ الصيانة والتنظيم

```bash
# تنظيف وتنظيم المشروع
python cleanup_and_organize.py

# عرض هيكل المشروع
cat PROJECT_STRUCTURE.md

# عرض ملخص الاختبارات
cat database/docs/TEST_SUMMARY.md
```

## 📖 التوثيق

- [📋 دليل التثبيت](docs/installation_guide.md)
- [📋 دليل المستخدم](docs/user_guide.md)
- [📋 دليل المطور](docs/developer_guide.md)
- [📋 هيكل المشروع](PROJECT_STRUCTURE.md)
- [📋 ملخص الاختبارات](database/docs/TEST_SUMMARY.md)
- [📋 ملخص الإصلاحات](database/docs/FIXES_SUMMARY.md)

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```json
{
  "database": {
    "mysql": {
      "host": "localhost",
      "port": 3306,
      "user": "root",
      "password": "your_password",
      "database": "anubis_system",
      "charset": "utf8mb4"
    }
  }
}
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

### معايير الكود
- اتباع معايير PEP 8
- إضافة اختبارات للميزات الجديدة
- توثيق الكود باللغة العربية والإنجليزية
- تشغيل جميع الاختبارات قبل الإرسال

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 🏺 حول أنوبيس

أنوبيس هو إله مصري قديم مرتبط بالحكمة والمعرفة والحماية. اخترنا هذا الاسم لنظامنا الذكي لأنه يجسد:

- 🧠 **الحكمة** - في تحليل وفهم المشاريع
- 🔍 **المعرفة** - في قاعدة البيانات الشاملة
- 🛡️ **الحماية** - في كشف الأخطاء والمشاكل
- ⚡ **القوة** - في الأداء والكفاءة

## 📞 الدعم والتواصل

- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [انضم إلى خادمنا](https://discord.gg/anubis-ai)
- 📱 **Twitter**: [@AnubisAI](https://twitter.com/AnubisAI)
- 🐛 **تقرير الأخطاء**: [GitHub Issues](https://github.com/your-username/Universal-AI-Assistants/issues)

---

**تم تطوير هذا النظام بحب وشغف لخدمة المجتمع التقني العربي** ❤️🏺

**الإصدار الحالي:** 2.0.0 - منظم ومحسن  
**آخر تحديث:** 14 يوليو 2025
"""
        
        readme_path = self.project_root / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        self.log_action("تحديث README الرئيسي", "تم تحديث ملف README.md")
    
    def save_cleanup_log(self):
        """حفظ سجل التنظيف"""
        log_file = self.archive_dir / f"cleanup_log_{self.timestamp}.json"
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.cleanup_log, f, ensure_ascii=False, indent=2)
        
        # إنشاء ملخص نصي
        summary_file = self.archive_dir / f"cleanup_summary_{self.timestamp}.md"
        
        summary_content = f"""# 🧹 ملخص تنظيف مشروع أنوبيس
# Anubis Project Cleanup Summary

**تاريخ التنظيف:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 إحصائيات العمليات

إجمالي العمليات المنجزة: {len(self.cleanup_log)}

## 📝 تفاصيل العمليات

"""
        
        for i, log_entry in enumerate(self.cleanup_log, 1):
            summary_content += f"{i}. **{log_entry['action']}**\n"
            if log_entry['details']:
                summary_content += f"   - {log_entry['details']}\n"
            summary_content += f"   - الوقت: {log_entry['timestamp']}\n\n"
        
        summary_content += """
## 🎯 النتيجة النهائية

تم تنظيف وتنظيم مشروع نظام أنوبيس بنجاح! 🎉

- ✅ تم حذف الملفات غير المستخدمة
- ✅ تم أرشفة الملفات القديمة والمكررة
- ✅ تم تنظيم ملفات قاعدة البيانات
- ✅ تم تحديث ملف README الرئيسي
- ✅ تم إنشاء هيكل منظم ونظيف

المشروع الآن نظيف ومنظم وجاهز للتطوير! 🏺✨
"""
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        print(f"\n📄 تم حفظ سجل التنظيف في: {log_file}")
        print(f"📄 تم حفظ ملخص التنظيف في: {summary_file}")
    
    def run_full_cleanup(self):
        """تشغيل التنظيف الشامل"""
        print("🧹 بدء التنظيف الشامل لمشروع نظام أنوبيس")
        print("=" * 60)
        
        # تنظيف التقارير المكررة
        self.clean_duplicate_reports()
        
        # تنظيف ملفات النسخ الاحتياطية
        self.clean_backup_files()
        
        # تنظيف السكريبتات غير المستخدمة
        self.clean_unused_scripts()
        
        # تنظيف الوثائق المكررة
        self.clean_redundant_docs()
        
        # تنظيف وثائق الجذر
        self.clean_project_root_docs()
        
        # تنظيف ملفات __pycache__
        self.clean_pycache()
        
        # تنظيم ملفات قاعدة البيانات
        self.organize_database_files()
        
        # تحديث ملف README الرئيسي
        self.update_main_readme()
        
        # حفظ سجل العمليات
        self.save_cleanup_log()
        
        print("\n" + "=" * 60)
        print("🎉 تم تنظيف المشروع بنجاح!")
        print(f"📁 الأرشيف: {self.archive_dir}")
        print(f"📊 العمليات المنجزة: {len(self.cleanup_log)}")


def main():
    """الدالة الرئيسية"""
    cleaner = AnubisProjectCleaner()
    cleaner.run_full_cleanup()


if __name__ == "__main__":
    main()
