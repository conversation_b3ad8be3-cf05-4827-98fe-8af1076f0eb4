#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏆 مشغل التحقق النهائي من قاعدة البيانات - نظام أنوبيس
Anubis Final Database Validation Runner

مشغل التحقق النهائي الشامل من قاعدة البيانات مع إنتاج التقارير
"""

import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any

from database_validator import DatabaseValidator

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class FinalValidationRunner:
    """🏆 مشغل التحقق النهائي من قاعدة البيانات"""
    
    def __init__(self, config_path: str = "configs/database_config.json"):
        """تهيئة مشغل التحقق النهائي"""
        self.validator = DatabaseValidator(config_path)
        self.start_time = None
        
    def run_final_validation(self) -> Dict[str, Any]:
        """تشغيل التحقق النهائي الشامل"""
        print("🏆 بدء التحقق النهائي من قاعدة بيانات نظام أنوبيس")
        print("=" * 60)
        
        self.start_time = time.time()
        
        # تشغيل جميع عمليات التحقق
        validations = [
            ("معلومات قاعدة البيانات", self.validator.validate_database_info),
            ("هيكل الجداول", self.validator.validate_table_structure),
            ("المفاتيح الخارجية", self.validator.validate_foreign_keys),
            ("جودة البيانات", self.validator.validate_data_quality),
            ("الأداء", self.validator.validate_performance)
        ]
        
        passed_validations = 0
        total_validations = len(validations)
        
        for validation_name, validation_function in validations:
            try:
                if validation_function():
                    passed_validations += 1
            except Exception as e:
                self.validator.log_validation(
                    validation_name, 
                    "FAIL", 
                    f"خطأ غير متوقع: {e}"
                )
        
        total_duration = time.time() - self.start_time
        
        # تلخيص النتائج
        print("\n" + "=" * 60)
        print("🎯 ملخص التحقق النهائي")
        print("=" * 60)
        
        success_rate = passed_validations / total_validations * 100
        
        if success_rate == 100:
            status_icon = "🎉"
            status_text = "قاعدة البيانات جاهزة للإنتاج!"
        elif success_rate >= 80:
            status_icon = "⚠️"
            status_text = "قاعدة البيانات جيدة مع بعض التحذيرات"
        else:
            status_icon = "❌"
            status_text = "قاعدة البيانات تحتاج إلى إصلاحات"
        
        print(f"{status_icon} النتيجة النهائية: {passed_validations}/"
              f"{total_validations} فحص نجح ({success_rate:.1f}%)")
        print(f"⏱️ وقت التحقق: {total_duration:.3f} ثانية")
        print(f"📋 الحالة: {status_text}")
        
        # إنشاء التقرير النهائي
        final_report = {
            'timestamp': datetime.now().isoformat(),
            'total_validations': total_validations,
            'passed_validations': passed_validations,
            'success_rate': success_rate,
            'total_duration': total_duration,
            'status': status_text,
            'ready_for_production': success_rate == 100,
            'validation_results': self.validator.validation_results,
            'database_config': {
                'host': self.validator.config['host'],
                'port': self.validator.config['port'],
                'database': self.validator.config['database']
            }
        }
        
        return final_report
    
    def save_report(self, report: Dict[str, Any]) -> str:
        """حفظ التقرير النهائي"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"database/final_validation_report_{timestamp}.json"
        
        # إنشاء المجلد إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                # تحويل البيانات إلى تنسيق قابل للتحويل إلى JSON
                json_safe_report = self.validator._convert_to_json_serializable(report)
                json.dump(json_safe_report, f, ensure_ascii=False, indent=2)
            
            print(f"\n📄 تم حفظ التقرير النهائي في: {report_file}")
            return report_file
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return ""
    
    def generate_html_report(self, report: Dict[str, Any]) -> str:
        """إنتاج تقرير HTML"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = f"database/final_validation_report_{timestamp}.html"
        
        # إنشاء محتوى HTML
        html_content = self._create_html_content(report)
        
        try:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"📄 تم إنتاج تقرير HTML: {html_file}")
            return html_file
            
        except Exception as e:
            print(f"❌ خطأ في إنتاج تقرير HTML: {e}")
            return ""
    
    def _create_html_content(self, report: Dict[str, Any]) -> str:
        """إنشاء محتوى HTML للتقرير"""
        success_rate = report.get('success_rate', 0)
        
        # تحديد لون الحالة
        if success_rate == 100:
            status_color = "#28a745"  # أخضر
        elif success_rate >= 80:
            status_color = "#ffc107"  # أصفر
        else:
            status_color = "#dc3545"  # أحمر
        
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير التحقق النهائي - نظام أنوبيس</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }}
        .status {{
            font-size: 2em;
            font-weight: bold;
            color: {status_color};
            margin: 20px 0;
        }}
        .metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .metric {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid {status_color};
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            color: {status_color};
        }}
        .metric-label {{
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }}
        .validation-results {{
            margin: 30px 0;
        }}
        .validation-item {{
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }}
        .validation-pass {{
            background-color: #d4edda;
            border-left-color: #28a745;
        }}
        .validation-warn {{
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }}
        .validation-fail {{
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #eee;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏺 تقرير التحقق النهائي من قاعدة بيانات نظام أنوبيس</h1>
            <p>تم إنتاج التقرير في: {report['timestamp']}</p>
        </div>
        
        <div class="status">
            {report['status']}
        </div>
        
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">{report['success_rate']:.1f}%</div>
                <div class="metric-label">معدل النجاح</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report['passed_validations']}/{report['total_validations']}</div>
                <div class="metric-label">الفحوصات الناجحة</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report['total_duration']:.3f}s</div>
                <div class="metric-label">وقت التحقق</div>
            </div>
            <div class="metric">
                <div class="metric-value">{'نعم' if report['ready_for_production'] else 'لا'}</div>
                <div class="metric-label">جاهز للإنتاج</div>
            </div>
        </div>
        
        <div class="validation-results">
            <h2>📋 تفاصيل الفحوصات</h2>
"""
        
        # إضافة نتائج الفحوصات
        for result in report.get('validation_results', []):
            status = result.get('status', 'UNKNOWN')
            css_class = f"validation-{status.lower()}"
            
            if status == "PASS":
                icon = "✅"
            elif status == "FAIL":
                icon = "❌"
            else:
                icon = "⚠️"
            
            html_content += f"""
            <div class="validation-item {css_class}">
                <h3>{icon} {result.get('check_name', 'فحص غير محدد')}</h3>
                <p><strong>الحالة:</strong> {status}</p>
                <p><strong>التفاصيل:</strong> {result.get('details', 'لا توجد تفاصيل')}</p>
                <p><strong>الوقت:</strong> {result.get('timestamp', 'غير محدد')}</p>
            </div>
"""
        
        html_content += """
        </div>
        
        <div class="footer">
            <p>تم إنتاج هذا التقرير بواسطة نظام أنوبيس للمساعدين الذكيين 🏺</p>
        </div>
    </div>
</body>
</html>
"""
        
        return html_content


def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء مشغل التحقق النهائي
        runner = FinalValidationRunner()
        
        # تشغيل التحقق النهائي
        report = runner.run_final_validation()
        
        # حفظ التقرير JSON
        json_file = runner.save_report(report)
        
        # إنتاج تقرير HTML
        html_file = runner.generate_html_report(report)
        
        # تحديد كود الخروج
        exit_code = 0 if report['ready_for_production'] else 1
        
        print(f"\n🎯 النتيجة النهائية: "
              f"{'نجح التحقق!' if exit_code == 0 else 'يحتاج إلى مراجعة'}")
        
        return exit_code
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التحقق النهائي: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
