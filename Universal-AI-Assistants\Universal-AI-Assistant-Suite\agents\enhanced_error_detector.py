#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 وكيل كشف الأخطاء المحسن بالذكاء الاصطناعي
Enhanced AI-Powered Error Detector Agent

وكيل ذكي لكشف الأخطاء في جميع أنواع المشاريع البرمجية (باك إند + فرونت إند)
تم تطويره بالتعاون مع Gemini CLI
"""

import os
import sys
import re
import ast
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# إضافة مسار core
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from base_agent import BaseAgent
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent


class EnhancedErrorDetectorAgent(BaseAgent):
    """🔍 وكيل كشف الأخطاء المحسن للتطوير الكامل - بالتعاون مع Gemini CLI"""
    
    def get_agent_type(self) -> str:
        return "enhanced_error_detector"
    
    def initialize_agent(self):
        """تهيئة وكيل كشف الأخطاء المحسن"""
        # اللغات المدعومة للتطوير الكامل (بناءً على نصائح Gemini)
        self.supported_languages = {
            # Backend Languages
            '.py': 'Python',
            '.js': 'JavaScript', 
            '.ts': 'TypeScript',
            '.php': 'PHP',
            '.java': 'Java',
            '.cs': 'C#',
            '.go': 'Go',
            '.rs': 'Rust',
            '.rb': 'Ruby',
            
            # Frontend Languages  
            '.jsx': 'React JSX',
            '.tsx': 'React TSX',
            '.vue': 'Vue.js',
            '.svelte': 'Svelte',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'SASS',
            '.less': 'LESS',
            
            # Config & Data
            '.json': 'JSON',
            '.yaml': 'YAML',
            '.yml': 'YAML',
            '.xml': 'XML',
            '.sql': 'SQL'
        }
        
        # أنماط الأخطاء للباك إند (محسنة بنصائح Gemini)
        self.backend_patterns = {
            'python': [
                (r'print\s*\([^)]*\)', 'استخدام print في الكود الإنتاجي', 'low'),
                (r'eval\s*\(', 'استخدام eval - خطر أمني عالي', 'high'),
                (r'exec\s*\(', 'استخدام exec - خطر أمني عالي', 'high'),
                (r'import\s+\*', 'استيراد جميع الوحدات - غير مستحسن', 'medium'),
                (r'except\s*:', 'التعامل مع الاستثناءات بشكل عام', 'medium'),
                (r'password\s*=\s*["\'][^"\']+["\']', 'كلمة مرور مكشوفة في الكود', 'high'),
                (r'api_key\s*=\s*["\'][^"\']+["\']', 'مفتاح API مكشوف في الكود', 'high'),
            ],
            'javascript': [
                (r'var\s+', 'استخدام var بدلاً من let/const', 'medium'),
                (r'eval\s*\(', 'استخدام eval - خطر أمني', 'high'),
                (r'innerHTML\s*=', 'استخدام innerHTML - خطر XSS محتمل', 'medium'),
                (r'document\.write', 'استخدام document.write - غير مستحسن', 'medium'),
                (r'==\s*[^=]', 'استخدام == بدلاً من ===', 'low'),
                (r'console\.log\s*\(', 'console.log في الكود الإنتاجي', 'low'),
            ]
        }
        
        # أنماط الأخطاء للفرونت إند (جديدة بناءً على Gemini)
        self.frontend_patterns = {
            'react': [
                (r'dangerouslySetInnerHTML', 'استخدام dangerouslySetInnerHTML - خطر XSS', 'high'),
                (r'useEffect\s*\(\s*[^,]+\s*\)', 'useEffect بدون dependencies array', 'medium'),
                (r'setState\s*\([^)]*\)\s*;?\s*setState', 'استدعاءات setState متتالية', 'medium'),
                (r'key=\{index\}', 'استخدام index كـ key في React', 'medium'),
                (r'onClick=\{.*\(\)\}', 'استدعاء دالة مباشر في onClick', 'low'),
            ],
            'vue': [
                (r'v-html\s*=', 'استخدام v-html - خطر XSS محتمل', 'medium'),
                (r'\$refs\.\w+', 'الاعتماد المفرط على refs', 'low'),
                (r'v-for.*:key', 'v-for بدون key مناسب', 'medium'),
            ],
            'css': [
                (r'!important', 'استخدام !important - تجنب إذا أمكن', 'low'),
                (r'position:\s*fixed', 'استخدام position fixed - تأكد من الاستجابة', 'low'),
                (r'overflow:\s*hidden', 'overflow hidden قد يخفي محتوى مهم', 'low'),
            ]
        }
        
        # أنماط أطر العمل (محسنة بنصائح Gemini)
        self.framework_patterns = {
            'django': [
                (r'User\.objects\.get\(', 'استخدام get() بدون try/except - خطر DoesNotExist', 'high'),
                (r'\.raw\s*\(', 'استخدام raw SQL - خطر SQL injection', 'high'),
                (r'mark_safe\s*\(', 'استخدام mark_safe - خطر XSS', 'medium'),
                (r'DEBUG\s*=\s*True', 'DEBUG=True في الإنتاج - خطر أمني', 'high'),
                (r'SECRET_KEY\s*=\s*["\'][^"\']+["\']', 'SECRET_KEY مكشوف في الكود', 'high'),
            ],
            'fastapi': [
                (r'@app\.\w+\s*\([^)]*\)\s*def\s+\w+\s*\([^)]*\):', 'endpoint بدون type hints', 'medium'),
                (r'request\.json\(\)', 'استخدام request.json() بدون validation', 'medium'),
                (r'Depends\s*\(\s*\)', 'Depends فارغ - تحقق من التبعيات', 'medium'),
            ],
            'express': [
                (r'app\.\w+\s*\([^,]*,\s*\([^)]*\)\s*=>', 'route handler بدون error handling', 'medium'),
                (r'req\.query\.\w+', 'استخدام query parameters بدون validation', 'medium'),
                (r'res\.send\s*\(.*req\.', 'إرسال بيانات المستخدم مباشرة - خطر XSS', 'high'),
            ],
            'nextjs': [
                (r'getServerSideProps.*console\.log', 'console.log في server-side code', 'low'),
                (r'process\.env\.\w+', 'متغيرات البيئة بدون fallback', 'medium'),
            ]
        }
        
        self.detected_errors = []
        self.scan_statistics = {
            'files_scanned': 0,
            'errors_found': 0,
            'high_severity': 0,
            'medium_severity': 0,
            'low_severity': 0
        }
        
        self.log_action("تهيئة وكيل كشف الأخطاء المحسن", "دعم كامل للباك إند والفرونت إند مع Gemini CLI")
    
    def detect_errors(self, target: str = None) -> Dict[str, Any]:
        """كشف الأخطاء - نقطة دخول رئيسية"""
        if target is None:
            return self.scan_entire_project()
        elif os.path.isfile(target):
            return self.scan_single_file(target)
        elif os.path.isdir(target):
            return self.scan_directory(target)
        else:
            return {'error': f'الهدف غير صالح: {target}'}
    
    def scan_single_file(self, file_path: str) -> Dict[str, Any]:
        """فحص ملف واحد بالطرق التقليدية والذكاء الاصطناعي"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {'error': f'الملف غير موجود: {file_path}'}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل نوع الملف
            file_info = self._analyze_file_type(file_path, content)
            
            # كشف الأخطاء التقليدي
            traditional_errors = self._detect_traditional_errors(content, file_info)
            
            # كشف الأخطاء بالذكاء الاصطناعي (إذا كان متاحاً)
            ai_errors = []
            if self.is_ai_enabled():
                ai_errors = self._detect_ai_errors(content, file_info)
            
            # تحليل الأمان
            security_issues = self._analyze_security(content, file_info)
            
            # تحليل الأداء
            performance_issues = self._analyze_performance(content, file_info)
            
            all_errors = traditional_errors + ai_errors + security_issues + performance_issues
            
            result = {
                'file_path': str(file_path),
                'file_info': file_info,
                'scan_timestamp': datetime.now().isoformat(),
                'errors': {
                    'traditional': traditional_errors,
                    'ai_detected': ai_errors,
                    'security': security_issues,
                    'performance': performance_issues
                },
                'summary': {
                    'total_issues': len(all_errors),
                    'severity_breakdown': self._calculate_severity_breakdown(all_errors),
                    'categories': self._categorize_errors(all_errors)
                }
            }
            
            # تحديث الإحصائيات
            self.scan_statistics['files_scanned'] += 1
            self.scan_statistics['errors_found'] += len(all_errors)
            
            return result
            
        except Exception as e:
            return {'error': f'خطأ في فحص الملف: {e}'}
    
    def _analyze_file_type(self, file_path: Path, content: str) -> Dict[str, Any]:
        """تحليل نوع الملف وتحديد السياق"""
        file_ext = file_path.suffix.lower()
        language = self.supported_languages.get(file_ext, 'unknown')
        
        # تحديد نوع المشروع والإطار
        framework = self._detect_framework(content, language)
        project_type = self._detect_project_type(file_path, content)
        
        return {
            'extension': file_ext,
            'language': language,
            'framework': framework,
            'project_type': project_type,
            'size': len(content),
            'lines': len(content.split('\n'))
        }
    
    def _detect_framework(self, content: str, language: str) -> str:
        """كشف إطار العمل المستخدم"""
        content_lower = content.lower()
        
        # أطر الباك إند
        if 'django' in content_lower or 'from django' in content_lower:
            return 'django'
        elif 'fastapi' in content_lower or 'from fastapi' in content_lower:
            return 'fastapi'
        elif 'flask' in content_lower or 'from flask' in content_lower:
            return 'flask'
        elif 'express' in content_lower and language == 'JavaScript':
            return 'express'
        
        # أطر الفرونت إند
        elif 'react' in content_lower or 'import react' in content_lower:
            return 'react'
        elif 'vue' in content_lower or '@vue' in content_lower:
            return 'vue'
        elif 'next' in content_lower or 'next.js' in content_lower:
            return 'nextjs'
        elif 'svelte' in content_lower:
            return 'svelte'
        
        return 'unknown'
    
    def _detect_project_type(self, file_path: Path, content: str) -> str:
        """تحديد نوع المشروع (باك إند، فرونت إند، فول ستاك)"""
        path_str = str(file_path).lower()
        
        # مؤشرات الباك إند
        backend_indicators = ['api', 'server', 'backend', 'models', 'views', 'controllers', 'routes']
        
        # مؤشرات الفرونت إند
        frontend_indicators = ['components', 'pages', 'views', 'assets', 'styles', 'public', 'src']
        
        if any(indicator in path_str for indicator in backend_indicators):
            return 'backend'
        elif any(indicator in path_str for indicator in frontend_indicators):
            return 'frontend'
        else:
            return 'general'

    def _detect_traditional_errors(self, content: str, file_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """كشف الأخطاء بالطرق التقليدية"""
        errors = []
        lines = content.split('\n')
        language = file_info['language'].lower()
        framework = file_info['framework']

        # اختيار الأنماط المناسبة
        patterns = []

        # أنماط اللغة
        if language in self.backend_patterns:
            patterns.extend(self.backend_patterns[language])

        # أنماط الفرونت إند
        if language in ['jsx', 'tsx'] or 'react' in framework:
            patterns.extend(self.frontend_patterns.get('react', []))
        elif language == 'vue.js' or 'vue' in framework:
            patterns.extend(self.frontend_patterns.get('vue', []))
        elif language in ['css', 'scss', 'sass']:
            patterns.extend(self.frontend_patterns.get('css', []))

        # أنماط الإطار
        if framework in self.framework_patterns:
            patterns.extend(self.framework_patterns[framework])

        # فحص كل سطر
        for line_num, line in enumerate(lines, 1):
            for pattern, description, severity in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    errors.append({
                        'type': 'pattern_match',
                        'line': line_num,
                        'code': line.strip(),
                        'description': description,
                        'severity': severity,
                        'category': self._determine_category(description)
                    })

        # فحص syntax للـ Python
        if language == 'python':
            syntax_errors = self._check_python_syntax(content)
            errors.extend(syntax_errors)

        return errors

    def _detect_ai_errors(self, content: str, file_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """كشف الأخطاء بالذكاء الاصطناعي"""
        if not self.is_ai_enabled():
            return []

        language = file_info['language']
        framework = file_info['framework']
        project_type = file_info['project_type']

        prompt = f"""
        حلل هذا الكود بعناية واكتشف الأخطاء والمشاكل:

        معلومات الملف:
        - اللغة: {language}
        - الإطار: {framework}
        - نوع المشروع: {project_type}

        الكود:
        ```{language.lower()}
        {content[:1500]}
        ```

        ابحث عن:
        1. أخطاء منطقية وبرمجية
        2. مشاكل الأمان (XSS, SQL Injection, etc.)
        3. مشاكل الأداء
        4. انتهاكات أفضل الممارسات
        5. أخطاء محتملة في وقت التشغيل
        6. مشاكل خاصة بـ {framework} إذا كان معروفاً

        قدم كل مشكلة في سطر منفصل بصيغة:
        - [نوع المشكلة] وصف المشكلة
        """

        try:
            ai_response = self.get_ai_analysis(prompt, {
                'language': language,
                'framework': framework,
                'project_type': project_type,
                'analysis_type': 'comprehensive_error_detection'
            })

            return self._parse_ai_errors(ai_response)

        except Exception as e:
            return [{'type': 'ai_error', 'description': f'خطأ في التحليل الذكي: {e}', 'severity': 'low'}]

    def _analyze_security(self, content: str, file_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تحليل الأمان"""
        security_issues = []

        # أنماط أمنية عامة
        security_patterns = [
            (r'password\s*=\s*["\'][^"\']+["\']', 'كلمة مرور مكشوفة في الكود', 'high'),
            (r'api_key\s*=\s*["\'][^"\']+["\']', 'مفتاح API مكشوف', 'high'),
            (r'secret\s*=\s*["\'][^"\']+["\']', 'مفتاح سري مكشوف', 'high'),
            (r'token\s*=\s*["\'][^"\']+["\']', 'رمز مميز مكشوف', 'medium'),
            (r'eval\s*\(', 'استخدام eval - خطر تنفيذ كود ضار', 'high'),
            (r'innerHTML\s*=.*\+', 'تجميع HTML ديناميكي - خطر XSS', 'medium'),
        ]

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            for pattern, description, severity in security_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    security_issues.append({
                        'type': 'security',
                        'line': line_num,
                        'code': line.strip(),
                        'description': description,
                        'severity': severity,
                        'category': 'security'
                    })

        return security_issues

    def _analyze_performance(self, content: str, file_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """تحليل الأداء"""
        performance_issues = []

        # أنماط مشاكل الأداء
        performance_patterns = [
            (r'for\s+\w+\s+in\s+range\s*\(\s*len\s*\(', 'استخدام range(len()) بدلاً من enumerate', 'low'),
            (r'\.append\s*\([^)]*\)\s*$', 'استخدام append في حلقة - فكر في list comprehension', 'low'),
            (r'document\.getElementById', 'استعلامات DOM متكررة - فكر في caching', 'medium'),
            (r'SELECT\s+\*\s+FROM', 'استعلام SELECT * - حدد الأعمدة المطلوبة', 'medium'),
        ]

        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            for pattern, description, severity in performance_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    performance_issues.append({
                        'type': 'performance',
                        'line': line_num,
                        'code': line.strip(),
                        'description': description,
                        'severity': severity,
                        'category': 'performance'
                    })

        return performance_issues

    def _parse_ai_errors(self, ai_response: str) -> List[Dict[str, Any]]:
        """تحليل استجابة الذكاء الاصطناعي"""
        errors = []
        lines = ai_response.split('\n')

        for line in lines:
            line = line.strip()
            if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                error_text = line.lstrip('-•* ').strip()
                if len(error_text) > 10:  # تجاهل الأسطر القصيرة
                    errors.append({
                        'type': 'ai_detected',
                        'description': error_text,
                        'severity': self._determine_ai_severity(error_text),
                        'category': self._determine_category(error_text),
                        'source': 'ai_analysis'
                    })

        return errors[:10]  # أقصى 10 أخطاء من الذكاء الاصطناعي

    def _check_python_syntax(self, content: str) -> List[Dict[str, Any]]:
        """فحص syntax للـ Python"""
        errors = []

        try:
            ast.parse(content)
        except SyntaxError as e:
            errors.append({
                'type': 'syntax_error',
                'line': e.lineno,
                'description': f'خطأ في البناء: {e.msg}',
                'severity': 'high',
                'category': 'syntax',
                'code': e.text.strip() if e.text else ''
            })
        except Exception as e:
            errors.append({
                'type': 'parse_error',
                'description': f'خطأ في تحليل الكود: {e}',
                'severity': 'medium',
                'category': 'syntax'
            })

        return errors

    def _determine_category(self, description: str) -> str:
        """تحديد فئة المشكلة"""
        description_lower = description.lower()

        if any(word in description_lower for word in ['أمني', 'security', 'خطر', 'xss', 'injection']):
            return 'security'
        elif any(word in description_lower for word in ['أداء', 'performance', 'بطيء', 'slow']):
            return 'performance'
        elif any(word in description_lower for word in ['syntax', 'بناء', 'تحليل']):
            return 'syntax'
        else:
            return 'quality'

    def _determine_ai_severity(self, description: str) -> str:
        """تحديد شدة المشكلة من تحليل الذكاء الاصطناعي"""
        description_lower = description.lower()

        if any(word in description_lower for word in ['خطر', 'عالي', 'critical', 'high', 'security']):
            return 'high'
        elif any(word in description_lower for word in ['متوسط', 'medium', 'warning']):
            return 'medium'
        else:
            return 'low'

    def _calculate_severity_breakdown(self, errors: List[Dict[str, Any]]) -> Dict[str, int]:
        """حساب توزيع الأخطاء حسب الشدة"""
        breakdown = {'high': 0, 'medium': 0, 'low': 0}

        for error in errors:
            severity = error.get('severity', 'low')
            if severity in breakdown:
                breakdown[severity] += 1

        return breakdown

    def _categorize_errors(self, errors: List[Dict[str, Any]]) -> Dict[str, int]:
        """تصنيف الأخطاء حسب الفئة"""
        categories = {'security': 0, 'performance': 0, 'quality': 0, 'syntax': 0}

        for error in errors:
            category = error.get('category', 'quality')
            if category in categories:
                categories[category] += 1

        return categories

    def _should_skip_file(self, file_path: Path) -> bool:
        """تحديد ما إذا كان يجب تخطي الملف"""
        skip_patterns = [
            'node_modules', '.git', '__pycache__', '.pytest_cache',
            'venv', 'env', '.env', 'dist', 'build', '.next',
            'coverage', '.coverage', 'htmlcov'
        ]

        path_str = str(file_path)
        return any(pattern in path_str for pattern in skip_patterns)

    def scan_directory(self, directory_path: str) -> Dict[str, Any]:
        """فحص مجلد محدد"""
        directory_path = Path(directory_path)

        if not directory_path.exists() or not directory_path.is_dir():
            return {'error': f'المجلد غير موجود: {directory_path}'}

        directory_results = {
            'directory_path': str(directory_path),
            'scan_timestamp': datetime.now().isoformat(),
            'files_scanned': {},
            'summary': {
                'total_files': 0,
                'files_with_issues': 0,
                'total_issues': 0,
                'by_severity': {'high': 0, 'medium': 0, 'low': 0}
            }
        }

        # فحص جميع الملفات في المجلد
        for ext in self.supported_languages.keys():
            files = list(directory_path.rglob(f'*{ext}'))

            for file_path in files:
                if self._should_skip_file(file_path):
                    continue

                file_result = self.scan_single_file(str(file_path))

                if 'error' not in file_result:
                    directory_results['files_scanned'][str(file_path)] = file_result
                    directory_results['summary']['total_files'] += 1

                    if file_result['summary']['total_issues'] > 0:
                        directory_results['summary']['files_with_issues'] += 1
                        directory_results['summary']['total_issues'] += file_result['summary']['total_issues']

        return directory_results

    def scan_entire_project(self) -> Dict[str, Any]:
        """فحص المشروع بالكامل"""
        return self.scan_directory(str(self.project_path))

    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        return {
            'agent_type': self.agent_type,
            'supported_languages': list(self.supported_languages.values()),
            'total_patterns': (
                sum(len(patterns) for patterns in self.backend_patterns.values()) +
                sum(len(patterns) for patterns in self.frontend_patterns.values()) +
                sum(len(patterns) for patterns in self.framework_patterns.values())
            ),
            'ai_enabled': self.is_ai_enabled(),
            'scan_statistics': self.scan_statistics.copy(),
            'last_scan': datetime.now().isoformat()
        }
