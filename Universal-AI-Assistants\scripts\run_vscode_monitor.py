#!/usr/bin/env python3
"""
Quick VS Code Process Monitor Runner
مشغل سريع لمراقب عمليات VS Code

This script provides a simple interface to run VS Code process monitoring.
"""

import os
import sys
import json
import subprocess
from datetime import datetime

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    try:
        import psutil
        print("✓ psutil library found")
        return True
    except ImportError:
        print("✗ psutil library not found")
        print("Please install it using: pip install psutil")
        return False

def run_single_check():
    """تشغيل فحص واحد"""
    print("Running single VS Code process check...")
    print("="*50)
    
    try:
        from vscode_process_monitor import VSCodeProcessMonitor
        monitor = VSCodeProcessMonitor()
        report, filename = monitor.run_full_check()
        
        print(f"\n✓ Check completed successfully!")
        print(f"Report saved to: {filename}")
        
        return True
    except Exception as e:
        print(f"✗ Error during check: {e}")
        return False

def run_continuous_monitoring():
    """تشغيل المراقبة المستمرة"""
    print("Starting continuous monitoring...")
    print("Press Ctrl+C to stop")
    print("="*50)
    
    try:
        from vscode_process_alerts import VSCodeProcessAlerts
        alerts = VSCodeProcessAlerts()
        alerts.continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    except Exception as e:
        print(f"✗ Error during monitoring: {e}")

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config_path = "Universal-AI-Assistants/configs/alert_config.json"
    
    if os.path.exists(config_path):
        print(f"Config file already exists: {config_path}")
        return
    
    config = {
        "thresholds": {
            "cpu_warning": 70,
            "cpu_critical": 90,
            "memory_warning": 70,
            "memory_critical": 90,
            "vscode_memory_warning": 20,
            "vscode_memory_critical": 40
        },
        "monitoring": {
            "interval": 60,
            "alert_cooldown": 300,
            "max_alerts_per_hour": 10
        },
        "notifications": {
            "console": True,
            "file": True,
            "email": False
        },
        "email": {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "sender_email": "",
            "sender_password": "",
            "recipient_email": ""
        }
    }
    
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✓ Config file created: {config_path}")

def show_system_info():
    """عرض معلومات النظام"""
    try:
        import psutil
        import platform
        
        print("System Information:")
        print("="*30)
        print(f"Platform: {platform.system()} {platform.release()}")
        print(f"CPU Count: {psutil.cpu_count()}")
        print(f"Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB")
        print(f"Current CPU: {psutil.cpu_percent(interval=1):.1f}%")
        print(f"Current Memory: {psutil.virtual_memory().percent:.1f}%")
        
        # البحث عن عمليات VS Code
        vscode_count = 0
        for proc in psutil.process_iter(['name']):
            try:
                if 'code' in proc.info['name'].lower():
                    vscode_count += 1
            except:
                pass
        
        print(f"VS Code Processes: {vscode_count}")
        print("="*30)
        
    except Exception as e:
        print(f"Error getting system info: {e}")

def install_dependencies():
    """تثبيت التبعيات"""
    print("Installing required dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        print("✓ psutil installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Error installing psutil: {e}")
        return False

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "="*60)
    print("VS CODE PROCESS MONITOR")
    print("="*60)
    print("1. Run single check")
    print("2. Start continuous monitoring")
    print("3. Show system information")
    print("4. Create/update config file")
    print("5. Install dependencies")
    print("6. View recent reports")
    print("0. Exit")
    print("="*60)

def view_recent_reports():
    """عرض التقارير الحديثة"""
    reports_dir = "Universal-AI-Assistants/reports"
    
    if not os.path.exists(reports_dir):
        print("No reports directory found")
        return
    
    # البحث عن ملفات التقارير
    report_files = []
    for file in os.listdir(reports_dir):
        if file.startswith("vscode_monitor_report_") and file.endswith(".json"):
            report_files.append(file)
    
    if not report_files:
        print("No VS Code monitor reports found")
        return
    
    # ترتيب حسب التاريخ
    report_files.sort(reverse=True)
    
    print(f"\nRecent VS Code Monitor Reports ({len(report_files)} found):")
    print("-" * 50)
    
    for i, file in enumerate(report_files[:10]):  # أحدث 10 تقارير
        file_path = os.path.join(reports_dir, file)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                report = json.load(f)
                timestamp = report.get('timestamp', 'Unknown')
                vscode_processes = report.get('summary', {}).get('vscode_processes', 0)
                system_health = report.get('summary', {}).get('system_health', 'Unknown')
                
                print(f"{i+1:2d}. {file}")
                print(f"    Time: {timestamp}")
                print(f"    VS Code Processes: {vscode_processes}")
                print(f"    System Health: {system_health}")
                print()
                
        except Exception as e:
            print(f"{i+1:2d}. {file} (Error reading: {e})")

def main():
    """الدالة الرئيسية"""
    # التحقق من المسار الحالي
    if not os.path.exists("Universal-AI-Assistants"):
        print("Warning: Not in the correct directory")
        print("Please run this script from the project root directory")
    
    while True:
        show_menu()
        
        try:
            choice = input("\nEnter your choice (0-6): ").strip()
            
            if choice == "0":
                print("Goodbye!")
                break
            elif choice == "1":
                if check_dependencies():
                    run_single_check()
                else:
                    print("Please install dependencies first (option 5)")
            elif choice == "2":
                if check_dependencies():
                    run_continuous_monitoring()
                else:
                    print("Please install dependencies first (option 5)")
            elif choice == "3":
                show_system_info()
            elif choice == "4":
                create_config_file()
            elif choice == "5":
                install_dependencies()
            elif choice == "6":
                view_recent_reports()
            else:
                print("Invalid choice. Please try again.")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
