#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 اختبار وكيل كشف الأخطاء المحسن
Test Enhanced Error Detector Agent

اختبار شامل للوكيل المحسن بالتعاون مع Gemini CLI
"""

import os
import sys
import json
from datetime import datetime

# إضافة مسارات المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

try:
    from agents.enhanced_error_detector import EnhancedErrorDetectorAgent
except ImportError as e:
    print(f"❌ خطأ في استيراد الوكيل المحسن: {e}")
    sys.exit(1)


def test_enhanced_error_detector():
    """اختبار الوكيل المحسن"""
    print("🧪 اختبار وكيل كشف الأخطاء المحسن - بالتعاون مع Gemini CLI")
    print("=" * 70)
    
    # إنشاء الوكيل المحسن
    try:
        agent = EnhancedErrorDetectorAgent(
            project_path=".",
            config={'enhanced_mode': True, 'ai_enabled': True},
            verbose=True
        )
        
        print(f"✅ تم إنشاء الوكيل المحسن - الذكاء الاصطناعي: {'مفعل' if agent.is_ai_enabled() else 'غير مفعل'}")
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الوكيل: {e}")
        return False
    
    # اختبار 1: فحص ملف Python
    print("\n🔍 اختبار 1: فحص ملف Python...")
    
    # إنشاء ملف اختبار Python
    test_python_code = '''
import os
password = "123456"  # كلمة مرور مكشوفة
api_key = "sk-test123"  # مفتاح API مكشوف

def bad_function():
    eval("print('hello')")  # استخدام eval خطر
    for i in range(len(items)):  # استخدام range(len())
        print(items[i])
    
    try:
        result = 10 / 0
    except:  # except عام
        pass
'''
    
    with open('test_python_file.py', 'w', encoding='utf-8') as f:
        f.write(test_python_code)
    
    python_result = agent.scan_single_file('test_python_file.py')
    
    if 'error' not in python_result:
        print(f"   ✅ تم فحص الملف بنجاح")
        print(f"   📊 إجمالي المشاكل: {python_result['summary']['total_issues']}")
        print(f"   🔍 الأخطاء التقليدية: {len(python_result['errors']['traditional'])}")
        print(f"   🧠 الأخطاء الذكية: {len(python_result['errors']['ai_detected'])}")
        print(f"   🔒 مشاكل الأمان: {len(python_result['errors']['security'])}")
        print(f"   ⚡ مشاكل الأداء: {len(python_result['errors']['performance'])}")
        
        # عرض بعض الأخطاء
        if python_result['errors']['traditional']:
            print(f"   📝 مثال على خطأ تقليدي: {python_result['errors']['traditional'][0]['description']}")
        
        if python_result['errors']['security']:
            print(f"   🔒 مثال على مشكلة أمنية: {python_result['errors']['security'][0]['description']}")
    else:
        print(f"   ❌ فشل في فحص الملف: {python_result['error']}")
    
    # اختبار 2: فحص ملف JavaScript/React
    print("\n🔍 اختبار 2: فحص ملف React...")
    
    test_react_code = '''
import React, { useState, useEffect } from 'react';

function BadComponent() {
    const [data, setData] = useState([]);
    
    useEffect(() => {
        fetchData();
    }); // useEffect بدون dependencies
    
    const handleClick = () => {
        eval("console.log('clicked')"); // استخدام eval
        document.getElementById('test').innerHTML = userInput; // XSS محتمل
    };
    
    return (
        <div>
            {data.map((item, index) => (
                <div key={index} onClick={handleClick()}> // key بـ index + استدعاء مباشر
                    <span dangerouslySetInnerHTML={{__html: item.content}} />
                </div>
            ))}
        </div>
    );
}

var oldVar = "استخدام var"; // استخدام var
console.log("في الإنتاج"); // console.log

export default BadComponent;
'''
    
    with open('test_react_file.jsx', 'w', encoding='utf-8') as f:
        f.write(test_react_code)
    
    react_result = agent.scan_single_file('test_react_file.jsx')
    
    if 'error' not in react_result:
        print(f"   ✅ تم فحص ملف React بنجاح")
        print(f"   📊 إجمالي المشاكل: {react_result['summary']['total_issues']}")
        print(f"   🎯 نوع المشروع: {react_result['file_info']['project_type']}")
        print(f"   🔧 الإطار المكتشف: {react_result['file_info']['framework']}")
        
        # عرض توزيع الأخطاء
        severity = react_result['summary']['severity_breakdown']
        print(f"   📈 توزيع الشدة - عالي: {severity['high']}, متوسط: {severity['medium']}, منخفض: {severity['low']}")
        
        categories = react_result['summary']['categories']
        print(f"   📂 الفئات - أمان: {categories['security']}, أداء: {categories['performance']}, جودة: {categories['quality']}")
    else:
        print(f"   ❌ فشل في فحص ملف React: {react_result['error']}")
    
    # اختبار 3: فحص المشروع الكامل (عينة صغيرة)
    print("\n🔍 اختبار 3: فحص المشروع (ملفات محدودة)...")
    
    # إنشاء ملف CSS للاختبار
    test_css_code = '''
.bad-style {
    position: fixed !important; /* استخدام !important و fixed */
    overflow: hidden; /* overflow hidden */
    color: red;
}

.another-class {
    background: blue !important;
}
'''
    
    with open('test_style.css', 'w', encoding='utf-8') as f:
        f.write(test_css_code)
    
    # فحص المشروع (سيفحص الملفات التي أنشأناها)
    project_result = agent.scan_entire_project()
    
    if 'error' not in project_result:
        print(f"   ✅ تم فحص المشروع بنجاح")
        print(f"   📁 الملفات المفحوصة: {project_result['summary']['total_files']}")
        print(f"   ⚠️ ملفات بها مشاكل: {project_result['summary']['files_with_issues']}")
        print(f"   📊 إجمالي المشاكل: {project_result['summary']['total_issues']}")
        
        # عرض الملفات المفحوصة
        print(f"   📋 الملفات:")
        for file_path in list(project_result['files_scanned'].keys())[:5]:  # أول 5 ملفات
            file_data = project_result['files_scanned'][file_path]
            print(f"      - {file_path}: {file_data['summary']['total_issues']} مشكلة")
    else:
        print(f"   ❌ فشل في فحص المشروع: {project_result['error']}")
    
    # اختبار 4: إحصائيات الوكيل
    print("\n📊 إحصائيات الوكيل المحسن:")
    stats = agent.get_error_statistics()
    
    print(f"   🤖 نوع الوكيل: {stats['agent_type']}")
    print(f"   🌐 اللغات المدعومة: {len(stats['supported_languages'])}")
    print(f"   🔍 إجمالي الأنماط: {stats['total_patterns']}")
    print(f"   🧠 الذكاء الاصطناعي: {'✅ مفعل' if stats['ai_enabled'] else '❌ غير مفعل'}")
    print(f"   📈 الملفات المفحوصة: {stats['scan_statistics']['files_scanned']}")
    print(f"   ⚠️ الأخطاء المكتشفة: {stats['scan_statistics']['errors_found']}")
    
    # تنظيف ملفات الاختبار
    cleanup_test_files()
    
    print("\n🏆 انتهى اختبار الوكيل المحسن!")
    return True


def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    test_files = ['test_python_file.py', 'test_react_file.jsx', 'test_style.css']
    
    for file_name in test_files:
        try:
            if os.path.exists(file_name):
                os.remove(file_name)
                print(f"   🗑️ تم حذف: {file_name}")
        except Exception as e:
            print(f"   ⚠️ لم يتم حذف {file_name}: {e}")


def generate_test_report(results):
    """إنتاج تقرير الاختبار"""
    report = {
        'test_timestamp': datetime.now().isoformat(),
        'agent_type': 'enhanced_error_detector',
        'test_results': results,
        'gemini_collaboration': True,
        'status': 'completed'
    }
    
    report_file = f"enhanced_error_detector_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 تم حفظ تقرير الاختبار: {report_file}")
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")


def main():
    """الدالة الرئيسية"""
    try:
        success = test_enhanced_error_detector()
        
        if success:
            print("\n✅ جميع الاختبارات نجحت!")
            print("🎯 الوكيل المحسن جاهز للاستخدام")
            print("📋 الخطوة التالية: إصلاح ProjectAnalyzerAgent مع Gemini CLI")
            return 0
        else:
            print("\n❌ بعض الاختبارات فشلت")
            return 1
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")
        return 2


if __name__ == "__main__":
    exit_code = main()
    print(f"\n🏺 انتهى اختبار الوكيل المحسن - كود الخروج: {exit_code}")
    sys.exit(exit_code)
