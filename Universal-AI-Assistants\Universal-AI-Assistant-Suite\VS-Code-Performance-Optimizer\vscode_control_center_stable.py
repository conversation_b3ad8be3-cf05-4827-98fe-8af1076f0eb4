# -*- coding: utf-8 -*-
"""
🚀 VS Code Control Center - النسخة المستقرة
==========================================

نسخة محسنة مع معالجة أفضل للأخطاء وحماية من KeyboardInterrupt
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import json
import signal
import sys
import os
from datetime import datetime

# معالجة إشارة KeyboardInterrupt
def signal_handler(sig, frame):
    print("\n🛑 تم إيقاف التطبيق بواسطة المستخدم")
    sys.exit(0)

signal.signal(signal.SIGINT, signal_handler)

# استيراد psutil مع معالجة الأخطاء
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    print("⚠️ تحذير: psutil غير متوفر")
    PSUTIL_AVAILABLE = False

# إضافة مسار الوكلاء
sys.path.append(os.path.join(os.path.dirname(__file__), 'agents'))

try:
    from agents.agent_coordinator import AgentCoordinator
    AGENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: سيعمل التطبيق بوضع أساسي - {e}")
    AGENTS_AVAILABLE = False

class SafeProcessMonitor:
    """مراقب آمن للعمليات مع معالجة محسنة للأخطاء"""
    
    @staticmethod
    def safe_process_iter(attrs=None):
        """تكرار آمن للعمليات مع معالجة الأخطاء"""
        if not PSUTIL_AVAILABLE:
            return []
        
        try:
            for proc in psutil.process_iter(attrs or []):
                try:
                    yield proc
                except (psutil.NoSuchProcess, psutil.AccessDenied, 
                        psutil.ZombieProcess, KeyboardInterrupt):
                    continue
                except Exception:
                    continue
        except KeyboardInterrupt:
            return
        except Exception:
            return
    
    @staticmethod
    def safe_get_process_info(proc, attrs):
        """الحصول الآمن على معلومات العملية"""
        try:
            info = {}
            for attr in attrs:
                try:
                    if attr == 'cpu_percent':
                        info[attr] = proc.cpu_percent()
                    elif attr == 'memory_percent':
                        info[attr] = proc.memory_percent()
                    elif attr == 'status':
                        info[attr] = proc.status()
                    elif attr == 'username':
                        info[attr] = proc.username()
                    else:
                        info[attr] = getattr(proc, attr)()
                except (psutil.NoSuchProcess, psutil.AccessDenied, 
                        psutil.ZombieProcess, KeyboardInterrupt):
                    info[attr] = 'N/A'
                except Exception:
                    info[attr] = 'N/A'
            return info
        except Exception:
            return {attr: 'N/A' for attr in attrs}
    
    @staticmethod
    def safe_system_stats():
        """إحصائيات آمنة للنظام"""
        if not PSUTIL_AVAILABLE:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'process_count': 0
            }
        
        try:
            stats = {}
            
            # المعالج
            try:
                stats['cpu_percent'] = psutil.cpu_percent(interval=0.1)
            except Exception:
                stats['cpu_percent'] = 0
            
            # الذاكرة
            try:
                memory = psutil.virtual_memory()
                stats['memory_percent'] = memory.percent
                stats['memory_available_gb'] = round(memory.available / (1024**3), 2)
            except Exception:
                stats['memory_percent'] = 0
                stats['memory_available_gb'] = 0
            
            # القرص
            try:
                disk = psutil.disk_usage('/')
                stats['disk_percent'] = disk.percent
            except Exception:
                stats['disk_percent'] = 0
            
            # العمليات
            try:
                stats['process_count'] = len(psutil.pids())
            except Exception:
                stats['process_count'] = 0
            
            return stats
            
        except Exception:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'disk_percent': 0,
                'process_count': 0
            }

class VSCodeControlCenterStable:
    """VS Code Control Center - النسخة المستقرة"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # نظام الوكلاء
        self.agent_coordinator = None
        self.agents_running = False
        
        # متغيرات البيانات
        self.processes_data = []
        self.selected_process = None
        self.auto_refresh = True
        self.chat_history = []
        self.running = True
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تهيئة نظام الوكلاء
        if AGENTS_AVAILABLE:
            self.initialize_agents()
        
        # بدء التحديث التلقائي
        self.start_auto_refresh()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🚀 VS Code Control Center - النسخة المستقرة")
        self.root.geometry("1400x900")
        self.root.configure(bg='#0d1117')
        
        # معالجة إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#0d1117')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # العنوان الرئيسي
        self.create_header(main_frame)
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='#0d1117')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # الجانب الأيسر - الإحصائيات والعمليات
        left_frame = tk.Frame(content_frame, bg='#0d1117')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # الجانب الأيمن - المحادثة والتحليل
        right_frame = tk.Frame(content_frame, bg='#0d1117')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # إنشاء الأقسام
        self.create_stats_section(left_frame)
        self.create_processes_section(left_frame)
        self.create_control_section(left_frame)
        
        self.create_chat_section(right_frame)
        self.create_analysis_section(right_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان الرئيسي"""
        header_frame = tk.Frame(parent, bg='#0d1117')
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        title_label = tk.Label(
            header_frame,
            text="🚀 VS Code Control Center - النسخة المستقرة",
            font=('Segoe UI', 28, 'bold'),
            fg='#58a6ff',
            bg='#0d1117'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            header_frame,
            text="مراقبة آمنة ومستقرة مع معالجة محسنة للأخطاء",
            font=('Segoe UI', 12),
            fg='#8b949e',
            bg='#0d1117'
        )
        subtitle_label.pack()
        
        # شريط الحالة العامة
        status_frame = tk.Frame(header_frame, bg='#0d1117')
        status_frame.pack(pady=(10, 0))
        
        self.overall_status_label = tk.Label(
            status_frame,
            text="🔄 جاري تحليل النظام...",
            font=('Segoe UI', 16, 'bold'),
            fg='#ffa657',
            bg='#0d1117'
        )
        self.overall_status_label.pack()
    
    def create_stats_section(self, parent):
        """إنشاء قسم الإحصائيات"""
        stats_frame = tk.LabelFrame(
            parent,
            text="📊 إحصائيات النظام المستقرة",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إطار الإحصائيات
        stats_inner = tk.Frame(stats_frame, bg='#161b22')
        stats_inner.pack(fill=tk.X, padx=15, pady=10)
        
        # الإحصائيات
        self.stats_labels = {}
        
        stats_data = [
            ("🖥️ العمليات", "processes", "0"),
            ("💾 الذاكرة", "memory", "0%"),
            ("⚡ المعالج", "cpu", "0%"),
            ("🧩 VS Code", "vscode", "0")
        ]
        
        for i, (title, key, default) in enumerate(stats_data):
            stat_frame = tk.Frame(stats_inner, bg='#21262d', relief='raised', bd=1)
            stat_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
            
            tk.Label(
                stat_frame,
                text=title,
                font=('Segoe UI', 10, 'bold'),
                fg='#8b949e',
                bg='#21262d'
            ).pack(pady=(5, 0))
            
            value_label = tk.Label(
                stat_frame,
                text=default,
                font=('Segoe UI', 14, 'bold'),
                fg='#58a6ff',
                bg='#21262d'
            )
            value_label.pack(pady=(0, 5))
            
            self.stats_labels[key] = value_label
    
    def create_processes_section(self, parent):
        """إنشاء قسم العمليات المبسط"""
        processes_frame = tk.LabelFrame(
            parent,
            text="📊 العمليات النشطة - عرض مستقر",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        processes_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # منطقة عرض العمليات
        self.processes_text = scrolledtext.ScrolledText(
            processes_frame,
            height=12,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.processes_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
    
    def create_control_section(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = tk.LabelFrame(
            parent,
            text="🎛️ لوحة التحكم الآمنة",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        control_frame.pack(fill=tk.X)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(control_frame, bg='#161b22')
        buttons_frame.pack(fill=tk.X, padx=15, pady=15)
        
        buttons = [
            ("🔄 تحديث", self.manual_update, '#238636'),
            ("🧹 تنظيف آمن", self.safe_cleanup, '#fb8500'),
            ("📊 تحليل النظام", self.analyze_system, '#1f6feb'),
            ("💾 حفظ التقرير", self.save_report, '#8957e5')
        ]
        
        for text, command, color in buttons:
            button = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Segoe UI', 10, 'bold'),
                fg='white',
                bg=color,
                activebackground=color,
                relief='flat',
                bd=0,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            button.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # مفتاح التحديث التلقائي
        auto_frame = tk.Frame(control_frame, bg='#161b22')
        auto_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_check = tk.Checkbutton(
            auto_frame,
            text="🔄 تحديث تلقائي آمن (كل 5 ثواني)",
            variable=self.auto_refresh_var,
            command=self.toggle_auto_refresh,
            font=('Segoe UI', 11, 'bold'),
            fg='#f0f6fc',
            bg='#161b22',
            selectcolor='#21262d',
            activebackground='#161b22',
            activeforeground='#58a6ff'
        )
        auto_check.pack(side=tk.LEFT)
    
    def create_chat_section(self, parent):
        """إنشاء قسم المحادثة"""
        chat_frame = tk.LabelFrame(
            parent,
            text="💬 محادثة آمنة مع النظام",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # منطقة المحادثة
        self.chat_text = scrolledtext.ScrolledText(
            chat_frame,
            height=10,
            font=('Segoe UI', 11),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.chat_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(15, 10))
        
        # إطار الإدخال
        input_frame = tk.Frame(chat_frame, bg='#161b22')
        input_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.chat_input = tk.Entry(
            input_frame,
            font=('Segoe UI', 12),
            bg='#21262d',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            relief='flat',
            bd=8
        )
        self.chat_input.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.chat_input.bind('<Return>', self.send_chat_message)
        
        send_button = tk.Button(
            input_frame,
            text="📤 إرسال",
            command=self.send_chat_message,
            font=('Segoe UI', 11, 'bold'),
            fg='white',
            bg='#238636',
            relief='flat',
            bd=0,
            padx=20,
            pady=6,
            cursor='hand2'
        )
        send_button.pack(side=tk.RIGHT)
        
        # رسائل ترحيبية
        self.add_chat_message("🤖 النظام", "مرحباً! أنا النسخة المستقرة من مساعدك الذكي")
        self.add_chat_message("💡 نصيحة", "اكتب 'تحليل' أو 'تنظيف' أو 'مساعدة'")
    
    def create_analysis_section(self, parent):
        """إنشاء قسم التحليل"""
        analysis_frame = tk.LabelFrame(
            parent,
            text="📊 تحليل مستقر ومراقبة",
            font=('Segoe UI', 14, 'bold'),
            fg='#58a6ff',
            bg='#161b22',
            bd=2,
            relief='groove'
        )
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        
        self.analysis_text = scrolledtext.ScrolledText(
            analysis_frame,
            height=10,
            font=('Consolas', 10),
            bg='#0d1117',
            fg='#f0f6fc',
            insertbackground='#58a6ff',
            selectbackground='#264f78',
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

    def initialize_agents(self):
        """تهيئة نظام الوكلاء بأمان"""
        try:
            self.agent_coordinator = AgentCoordinator()
            self.agent_coordinator.start_all_agents()
            self.agents_running = True

            self.add_chat_message("🤖 النظام", "✅ تم تهيئة الوكلاء الذكيين بنجاح!")
            self.log_analysis("✅ نظام الوكلاء الذكيين جاهز للعمل")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تهيئة الوكلاء: {e}")
            self.log_analysis(f"❌ خطأ في تهيئة الوكلاء: {e}")

    def start_auto_refresh(self):
        """بدء التحديث التلقائي الآمن"""
        if self.running and self.auto_refresh:
            try:
                self.refresh_all_data()
            except Exception as e:
                self.log_analysis(f"❌ خطأ في التحديث: {e}")

            # جدولة التحديث التالي
            self.root.after(5000, self.start_auto_refresh)  # كل 5 ثواني

    def refresh_all_data(self):
        """تحديث جميع البيانات بأمان"""
        try:
            self.update_stats()
            self.update_processes_display()

        except KeyboardInterrupt:
            return
        except Exception as e:
            self.log_analysis(f"❌ خطأ في تحديث البيانات: {e}")

    def update_stats(self):
        """تحديث الإحصائيات بأمان"""
        try:
            stats = SafeProcessMonitor.safe_system_stats()

            # تحديث العمليات
            self.stats_labels['processes'].configure(text=str(stats['process_count']))

            # تحديث الذاكرة
            memory_percent = stats['memory_percent']
            self.stats_labels['memory'].configure(
                text=f"{memory_percent:.1f}%",
                fg=self.get_status_color(memory_percent, 70, 85)
            )

            # تحديث المعالج
            cpu_percent = stats['cpu_percent']
            self.stats_labels['cpu'].configure(
                text=f"{cpu_percent:.1f}%",
                fg=self.get_status_color(cpu_percent, 60, 80)
            )

            # تحديث VS Code
            vscode_count = self.count_vscode_processes()
            self.stats_labels['vscode'].configure(
                text=str(vscode_count),
                fg=self.get_status_color(vscode_count, 5, 10)
            )

            # تحديث الحالة العامة
            self.update_overall_status(memory_percent, cpu_percent, vscode_count)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في تحديث الإحصائيات: {e}")

    def count_vscode_processes(self):
        """عد عمليات VS Code بأمان"""
        try:
            count = 0
            for proc in SafeProcessMonitor.safe_process_iter(['name']):
                try:
                    info = SafeProcessMonitor.safe_get_process_info(proc, ['name'])
                    if 'code' in info.get('name', '').lower():
                        count += 1
                except Exception:
                    continue
            return count
        except Exception:
            return 0

    def update_processes_display(self):
        """تحديث عرض العمليات بأمان"""
        try:
            self.processes_text.configure(state=tk.NORMAL)
            self.processes_text.delete(1.0, tk.END)

            # عنوان العرض
            header = "📊 العمليات النشطة (أعلى 10 عمليات حسب استهلاك المعالج):\n\n"
            self.processes_text.insert(tk.END, header)

            # جمع العمليات
            processes = []
            for proc in SafeProcessMonitor.safe_process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    info = SafeProcessMonitor.safe_get_process_info(proc, ['pid', 'name', 'cpu_percent', 'memory_percent'])
                    if info.get('cpu_percent', 0) > 0:  # فقط العمليات النشطة
                        processes.append(info)
                except Exception:
                    continue

            # ترتيب العمليات
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)

            # عرض أعلى 10 عمليات
            for i, proc in enumerate(processes[:10], 1):
                pid = proc.get('pid', 'N/A')
                name = proc.get('name', 'Unknown')
                cpu = proc.get('cpu_percent', 0)
                memory = proc.get('memory_percent', 0)

                # تحديد لون العملية
                if cpu > 20:
                    status = "🔴"
                elif cpu > 10:
                    status = "🟡"
                else:
                    status = "🟢"

                line = f"{i:2d}. {status} {name:<20} PID:{pid:<8} CPU:{cpu:5.1f}% MEM:{memory:5.1f}%\n"
                self.processes_text.insert(tk.END, line)

            # إحصائيات VS Code
            vscode_procs = [p for p in processes if 'code' in p.get('name', '').lower()]
            if vscode_procs:
                self.processes_text.insert(tk.END, f"\n🧩 عمليات VS Code ({len(vscode_procs)}):\n")
                for proc in vscode_procs[:5]:
                    name = proc.get('name', 'Unknown')
                    cpu = proc.get('cpu_percent', 0)
                    memory = proc.get('memory_percent', 0)
                    line = f"   • {name:<15} CPU:{cpu:5.1f}% MEM:{memory:5.1f}%\n"
                    self.processes_text.insert(tk.END, line)

            self.processes_text.configure(state=tk.DISABLED)

        except Exception as e:
            self.log_analysis(f"❌ خطأ في عرض العمليات: {e}")

    def get_status_color(self, value, warning_threshold, critical_threshold):
        """الحصول على لون الحالة"""
        if value >= critical_threshold:
            return '#f85149'  # أحمر
        elif value >= warning_threshold:
            return '#ffa657'  # برتقالي
        else:
            return '#3fb950'  # أخضر

    def update_overall_status(self, memory_percent, cpu_percent, vscode_count):
        """تحديث الحالة العامة"""
        try:
            if memory_percent > 90 or cpu_percent > 90:
                status = "🚨 النظام يحتاج تدخل فوري!"
                color = '#f85149'
            elif memory_percent > 75 or cpu_percent > 75:
                status = "⚠️ النظام يحتاج مراقبة"
                color = '#ffa657'
            elif vscode_count > 15:
                status = "🔄 VS Code يحتاج إعادة تشغيل"
                color = '#ffa657'
            else:
                status = "✅ النظام يعمل بكفاءة ممتازة"
                color = '#3fb950'

            self.overall_status_label.configure(text=status, fg=color)
        except Exception:
            pass

    def add_chat_message(self, sender, message):
        """إضافة رسالة إلى المحادثة"""
        try:
            self.chat_text.configure(state=tk.NORMAL)
            timestamp = datetime.now().strftime("%H:%M:%S")

            formatted_message = f"[{timestamp}] {sender}: {message}\n\n"

            self.chat_text.insert(tk.END, formatted_message)
            self.chat_text.configure(state=tk.DISABLED)
            self.chat_text.see(tk.END)

            # حفظ في التاريخ
            self.chat_history.append({
                'timestamp': timestamp,
                'sender': sender,
                'message': message
            })
        except Exception:
            pass

    def send_chat_message(self, event=None):
        """إرسال رسالة في المحادثة"""
        try:
            message = self.chat_input.get().strip()
            if not message:
                return

            self.chat_input.delete(0, tk.END)
            self.add_chat_message("👤 أنت", message)
            self.process_chat_command(message)
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في معالجة الرسالة: {e}")

    def process_chat_command(self, message):
        """معالجة أوامر المحادثة"""
        try:
            message_lower = message.lower()

            if any(word in message_lower for word in ['تحليل', 'حلل', 'فحص']):
                self.analyze_system()
            elif any(word in message_lower for word in ['تنظيف', 'نظف', 'تنظيف']):
                self.safe_cleanup()
            elif any(word in message_lower for word in ['vs code', 'vscode', 'كود']):
                self.analyze_vscode()
            elif any(word in message_lower for word in ['ذاكرة', 'memory', 'ram']):
                self.analyze_memory()
            elif any(word in message_lower for word in ['معالج', 'cpu', 'processor']):
                self.analyze_cpu()
            elif any(word in message_lower for word in ['مساعدة', 'help', 'أوامر']):
                self.show_help()
            else:
                # استخدام الوكلاء الذكيين إذا كانوا متاحين
                if AGENTS_AVAILABLE and self.agent_coordinator:
                    self.ask_ai_agents(message)
                else:
                    self.add_chat_message("🤖 النظام", "لم أفهم الأمر. اكتب 'مساعدة' لرؤية الأوامر المتاحة.")
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في معالجة الأمر: {e}")

    def log_analysis(self, message):
        """تسجيل رسالة في قسم التحليل"""
        try:
            self.analysis_text.configure(state=tk.NORMAL)
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.analysis_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.analysis_text.configure(state=tk.DISABLED)
            self.analysis_text.see(tk.END)
        except Exception:
            pass

    def manual_update(self):
        """تحديث يدوي"""
        try:
            self.add_chat_message("🤖 النظام", "🔄 تحديث يدوي للبيانات...")
            self.refresh_all_data()
            self.add_chat_message("🤖 النظام", "✅ تم التحديث بنجاح")
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في التحديث: {e}")

    def safe_cleanup(self):
        """تنظيف آمن للنظام"""
        try:
            self.add_chat_message("🤖 النظام", "🧹 بدء التنظيف الآمن...")

            cleaned_items = []

            # تنظيف الذاكرة
            import gc
            gc.collect()
            cleaned_items.append("✅ تم تنظيف الذاكرة")

            # تقرير التنظيف
            if cleaned_items:
                result = "🧹 تم التنظيف الآمن بنجاح:\n" + "\n".join(cleaned_items)
            else:
                result = "✅ النظام نظيف بالفعل!"

            self.add_chat_message("🤖 النظام", result)
            self.log_analysis("تم إجراء تنظيف آمن للنظام")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في التنظيف: {e}")

    def analyze_system(self):
        """تحليل شامل للنظام"""
        try:
            self.add_chat_message("🤖 النظام", "🔍 بدء التحليل الشامل للنظام...")

            stats = SafeProcessMonitor.safe_system_stats()

            analysis = f"""
📊 تحليل النظام الشامل:

💻 المعالج: {stats['cpu_percent']:.1f}% - {'🔴 مرتفع' if stats['cpu_percent'] > 80 else '🟡 متوسط' if stats['cpu_percent'] > 60 else '🟢 طبيعي'}
💾 الذاكرة: {stats['memory_percent']:.1f}% - {'🔴 مرتفع' if stats['memory_percent'] > 85 else '🟡 متوسط' if stats['memory_percent'] > 70 else '🟢 طبيعي'}
💿 القرص: {stats['disk_percent']:.1f}% - {'🔴 ممتلئ' if stats['disk_percent'] > 90 else '🟡 يحتاج تنظيف' if stats['disk_percent'] > 80 else '🟢 مساحة جيدة'}

🖥️ العمليات: {stats['process_count']} عملية نشطة
"""

            # تحليل VS Code
            vscode_count = self.count_vscode_processes()
            if vscode_count > 0:
                analysis += f"🧩 VS Code: {vscode_count} عملية نشطة\n"
            else:
                analysis += "🧩 VS Code: غير مفتوح\n"

            # توصيات
            recommendations = []
            if stats['cpu_percent'] > 80:
                recommendations.append("⚡ إغلاق العمليات عالية استهلاك المعالج")
            if stats['memory_percent'] > 85:
                recommendations.append("💾 تحرير الذاكرة وإغلاق التطبيقات غير الضرورية")
            if stats['disk_percent'] > 90:
                recommendations.append("💿 تنظيف القرص الصلب")
            if vscode_count > 10:
                recommendations.append("🧩 إعادة تشغيل VS Code")

            if recommendations:
                analysis += "\n💡 التوصيات:\n" + "\n".join(f"• {rec}" for rec in recommendations)
            else:
                analysis += "\n✅ النظام يعمل بكفاءة جيدة!"

            self.add_chat_message("🤖 النظام", analysis)
            self.log_analysis(f"تم إجراء تحليل شامل - المعالج: {stats['cpu_percent']:.1f}%, الذاكرة: {stats['memory_percent']:.1f}%")

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في التحليل: {e}")

    def analyze_vscode(self):
        """تحليل VS Code بالتفصيل"""
        try:
            self.add_chat_message("🤖 النظام", "🧩 تحليل VS Code...")

            vscode_procs = []
            total_cpu = 0
            total_memory = 0

            for proc in SafeProcessMonitor.safe_process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    info = SafeProcessMonitor.safe_get_process_info(proc, ['pid', 'name', 'cpu_percent', 'memory_percent'])
                    if 'code' in info.get('name', '').lower():
                        vscode_procs.append(info)
                        total_cpu += info.get('cpu_percent', 0)
                        total_memory += info.get('memory_percent', 0)
                except Exception:
                    continue

            if not vscode_procs:
                self.add_chat_message("🤖 النظام", "🧩 VS Code غير مفتوح حالياً")
                return

            analysis = f"""
🧩 تحليل VS Code:

📊 الإحصائيات:
• العمليات: {len(vscode_procs)}
• استهلاك المعالج: {total_cpu:.1f}%
• استهلاك الذاكرة: {total_memory:.1f}%

📋 العمليات النشطة:
"""

            for proc in vscode_procs[:5]:  # أول 5 عمليات
                pid = proc.get('pid', 'N/A')
                name = proc.get('name', 'Unknown')
                cpu = proc.get('cpu_percent', 0)
                memory = proc.get('memory_percent', 0)
                analysis += f"• {name} (PID: {pid}) - CPU: {cpu:.1f}%, Memory: {memory:.1f}%\n"

            # تقييم الحالة
            if total_cpu > 50 or total_memory > 30:
                analysis += "\n🔴 تحذير: استهلاك عالي! يُنصح بإعادة تشغيل VS Code"
            elif total_cpu > 25 or total_memory > 15:
                analysis += "\n🟡 استهلاك متوسط - مراقبة مطلوبة"
            else:
                analysis += "\n🟢 VS Code يعمل بكفاءة جيدة"

            self.add_chat_message("🤖 النظام", analysis)

        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل VS Code: {e}")

    def analyze_memory(self):
        """تحليل الذاكرة"""
        try:
            stats = SafeProcessMonitor.safe_system_stats()

            analysis = f"""
💾 تحليل الذاكرة:

📊 الإحصائيات:
• المستخدم: {stats['memory_percent']:.1f}%
• المتاح: {stats.get('memory_available_gb', 0):.1f} GB

{'🔴 تحذير: استهلاك عالي جداً!' if stats['memory_percent'] > 90 else '🟡 استهلاك مرتفع' if stats['memory_percent'] > 75 else '🟢 استهلاك طبيعي'}
"""

            self.add_chat_message("🤖 النظام", analysis)
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل الذاكرة: {e}")

    def analyze_cpu(self):
        """تحليل المعالج"""
        try:
            stats = SafeProcessMonitor.safe_system_stats()

            analysis = f"""
⚡ تحليل المعالج:

📊 الإحصائيات:
• الاستخدام: {stats['cpu_percent']:.1f}%

{'🔴 تحذير: حمولة عالية جداً!' if stats['cpu_percent'] > 90 else '🟡 حمولة مرتفعة' if stats['cpu_percent'] > 70 else '🟢 أداء طبيعي'}
"""

            self.add_chat_message("🤖 النظام", analysis)
        except Exception as e:
            self.add_chat_message("🤖 النظام", f"❌ خطأ في تحليل المعالج: {e}")

    def show_help(self):
        """عرض المساعدة والأوامر المتاحة"""
        help_text = """
💡 الأوامر المتاحة:

🔍 أوامر التحليل:
• "تحليل النظام" - تحليل شامل للنظام
• "تحليل VS Code" - تحليل VS Code بالتفصيل
• "تحليل الذاكرة" - تحليل استهلاك الذاكرة
• "تحليل المعالج" - تحليل أداء المعالج

🧹 أوامر التنظيف:
• "تنظيف النظام" - تنظيف آمن
• "تنظيف الذاكرة" - تحرير الذاكرة

❓ أوامر أخرى:
• "مساعدة" - عرض هذه القائمة
• يمكنك أيضاً طرح أسئلة مفتوحة!
"""

        self.add_chat_message("🤖 النظام", help_text)

    def ask_ai_agents(self, question):
        """سؤال الوكلاء الذكيين"""
        if not AGENTS_AVAILABLE or not self.agent_coordinator:
            self.add_chat_message("🤖 النظام", "❌ الوكلاء الذكيين غير متاحين")
            return

        self.add_chat_message("🤖 النظام", "🔍 جاري البحث عن إجابة من الوكلاء الذكيين...")

        def ask_agents():
            try:
                responses = self.agent_coordinator.ask_ai_agents(question)

                for agent_name, response in responses.items():
                    self.root.after(0, lambda a=agent_name, r=response:
                                   self.add_chat_message(f"🤖 {a.upper()}", r))

            except Exception as e:
                self.root.after(0, lambda:
                               self.add_chat_message("🤖 النظام", f"❌ خطأ في الوكلاء: {e}"))

        threading.Thread(target=ask_agents, daemon=True).start()

    def save_report(self):
        """حفظ تقرير مفصل"""
        try:
            # جمع البيانات
            stats = SafeProcessMonitor.safe_system_stats()

            report_data = {
                'timestamp': datetime.now().isoformat(),
                'system_stats': stats,
                'vscode_processes': self.count_vscode_processes(),
                'chat_history': self.chat_history[-20:]  # آخر 20 رسالة
            }

            filename = f"vscode_stable_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)

            self.add_chat_message("🤖 النظام", f"💾 تم حفظ التقرير: {filename}")
            messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}")

        except Exception as e:
            error_msg = f"❌ فشل في حفظ التقرير: {e}"
            self.add_chat_message("🤖 النظام", error_msg)
            messagebox.showerror("خطأ", error_msg)

    def toggle_auto_refresh(self):
        """تبديل التحديث التلقائي"""
        self.auto_refresh = self.auto_refresh_var.get()

        if self.auto_refresh:
            self.add_chat_message("🤖 النظام", "🔄 تم تفعيل التحديث التلقائي الآمن")
            self.start_auto_refresh()
        else:
            self.add_chat_message("🤖 النظام", "⏹️ تم إيقاف التحديث التلقائي")

    def on_closing(self):
        """معالجة إغلاق النافذة"""
        try:
            self.running = False
            if self.agent_coordinator:
                self.agent_coordinator.stop_all_agents()
            self.root.quit()
            self.root.destroy()
        except Exception:
            pass

    def run(self):
        """تشغيل التطبيق"""
        try:
            # رسالة ترحيبية
            self.add_chat_message("🤖 النظام", "🚀 مرحباً بك في النسخة المستقرة!")
            self.log_analysis("تم تشغيل VS Code Control Center - النسخة المستقرة")

            # بدء التطبيق
            self.root.mainloop()

        except KeyboardInterrupt:
            self.on_closing()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التطبيق:\n{e}")

if __name__ == "__main__":
    try:
        app = VSCodeControlCenterStable()
        app.run()
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف التطبيق")
        sys.exit(0)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
