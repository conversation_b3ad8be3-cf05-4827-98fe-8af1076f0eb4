"""
🤖 وكلاء النظام الذكيين
Universal AI Assistants Agents
"""

__version__ = "1.0.0"
__description__ = "مجموعة الوكلاء الأذكياء للنظام العالمي"

# استيراد الوكلاء المتاحين
try:
    from .database_agent import DatabaseAgent
except ImportError:
    DatabaseAgent = None

try:
    from .file_organizer_agent import FileOrganizerAgent
except ImportError:
    FileOrganizerAgent = None

try:
    from .memory_agent import MemoryAgent
except ImportError:
    MemoryAgent = None

try:
    from .error_detector_agent import ErrorDetectorAgent
except ImportError:
    ErrorDetectorAgent = None

try:
    from .project_analyzer_agent import ProjectAnalyzerAgent
except ImportError:
    ProjectAnalyzerAgent = None

# قائمة الوكلاء المتاحين
AVAILABLE_AGENTS = {
    'database_agent': DatabaseAgent,
    'file_organizer_agent': FileOrganizerAgent,
    'memory_agent': MemoryAgent,
    'error_detector_agent': ErrorDetectorAgent,
    'project_analyzer_agent': ProjectAnalyzerAgent
}

# تصفية الوكلاء المتاحين فقط
AVAILABLE_AGENTS = {k: v for k, v in AVAILABLE_AGENTS.items() if v is not None}

__all__ = [
    'DatabaseAgent',
    'FileOrganizerAgent', 
    'MemoryAgent',
    'ErrorDetectorAgent',
    'ProjectAnalyzerAgent',
    'AVAILABLE_AGENTS'
]
