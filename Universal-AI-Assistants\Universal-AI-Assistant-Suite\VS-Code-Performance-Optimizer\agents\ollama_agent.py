# -*- coding: utf-8 -*-
"""
🦙 وكيل Ollama - Ollama Agent
============================

وكيل ذكي يستخدم Ollama للتحليل المحلي والتوصيات الذكية
"""

import subprocess
import json
import time
try:
    import requests
except ImportError:
    requests = None
from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent

class OllamaAgent(BaseAgent):
    """وكيل Ollama للتحليل المحلي"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("OllamaAgent", config)
        self.ollama_available = self._check_ollama_availability()
        self.model = config.get('ollama_model', 'llama2') if config else 'llama2'
        self.api_url = config.get('ollama_api_url', 'http://localhost:11434') if config else 'http://localhost:11434'
        
    def _check_ollama_availability(self) -> bool:
        """التحقق من توفر Ollama"""
        try:
            # التحقق من CLI
            result = subprocess.run(['ollama', 'list'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                return True
            
            # التحقق من API
            response = requests.get(f"{self.api_url}/api/tags", timeout=5)
            return response.status_code == 200
            
        except (subprocess.TimeoutExpired, FileNotFoundError, requests.RequestException):
            self.logger.warning("⚠️ Ollama غير متوفر")
            return False
    
    def analyze(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل محلي باستخدام Ollama"""
        if not self.ollama_available:
            return {
                'error': 'Ollama غير متوفر',
                'fallback_analysis': self._fallback_analysis(data)
            }
        
        try:
            # إعداد البيانات للتحليل
            analysis_prompt = self._create_analysis_prompt(data)
            
            # استدعاء Ollama
            ollama_response = self._call_ollama(analysis_prompt)
            
            # معالجة الاستجابة
            analysis = self._process_ollama_response(ollama_response)
            
            # إضافة معلومات إضافية
            analysis.update({
                'timestamp': time.time(),
                'agent': 'OllamaAgent',
                'model_used': self.model,
                'data_source': 'ollama_local'
            })
            
            self.save_analysis(analysis)
            return analysis
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في تحليل Ollama: {e}")
            return {
                'error': str(e),
                'fallback_analysis': self._fallback_analysis(data)
            }
    
    def _create_analysis_prompt(self, data: Dict[str, Any]) -> str:
        """إنشاء prompt للتحليل"""
        prompt = f"""
You are a system performance expert specializing in VS Code optimization. Analyze the following data and provide intelligent recommendations:

Data:
{json.dumps(data, indent=2)}

Please provide:
1. Comprehensive system analysis
2. Identify performance bottlenecks
3. Specific optimization recommendations
4. VS Code health assessment
5. Preventive maintenance suggestions

Respond in JSON format with these fields:
- system_analysis: system analysis
- performance_issues: detected issues
- recommendations: recommendations
- priority_actions: high priority actions
- health_score: health score (out of 100)

Respond in Arabic when possible.
"""
        return prompt
    
    def _call_ollama(self, prompt: str) -> str:
        """استدعاء Ollama"""
        try:
            # محاولة استخدام API أولاً
            return self._call_ollama_api(prompt)
        except Exception as api_error:
            self.logger.warning(f"فشل API، محاولة CLI: {api_error}")
            try:
                return self._call_ollama_cli(prompt)
            except Exception as cli_error:
                raise Exception(f"فشل كل من API و CLI: {api_error}, {cli_error}")
    
    def _call_ollama_api(self, prompt: str) -> str:
        """استدعاء Ollama عبر API"""
        payload = {
            "model": self.model,
            "prompt": prompt,
            "stream": False
        }
        
        response = requests.post(
            f"{self.api_url}/api/generate",
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '')
        else:
            raise Exception(f"Ollama API error: {response.status_code}")
    
    def _call_ollama_cli(self, prompt: str) -> str:
        """استدعاء Ollama عبر CLI"""
        try:
            # إنشاء ملف مؤقت للـ prompt
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(prompt)
                prompt_file = f.name
            
            # استدعاء Ollama
            cmd = ['ollama', 'run', self.model, f'< {prompt_file}']
            result = subprocess.run(' '.join(cmd), shell=True, capture_output=True, text=True, timeout=60)
            
            # تنظيف الملف المؤقت
            import os
            os.unlink(prompt_file)
            
            if result.returncode == 0:
                return result.stdout
            else:
                raise Exception(f"Ollama CLI error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("انتهت مهلة انتظار Ollama")
        except Exception as e:
            raise Exception(f"خطأ في استدعاء Ollama CLI: {e}")
    
    def _process_ollama_response(self, response: str) -> Dict[str, Any]:
        """معالجة استجابة Ollama"""
        try:
            # محاولة استخراج JSON من الاستجابة
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # إذا لم يكن JSON، قم بتحليل النص
                return self._parse_text_response(response)
                
        except json.JSONDecodeError:
            return self._parse_text_response(response)
    
    def _parse_text_response(self, response: str) -> Dict[str, Any]:
        """تحليل الاستجابة النصية"""
        lines = response.strip().split('\n')
        
        analysis = {
            'system_analysis': 'تحليل محلي بواسطة Ollama',
            'performance_issues': [],
            'recommendations': [],
            'priority_actions': [],
            'health_score': 70,  # افتراضي
            'raw_response': response
        }
        
        current_section = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # تحديد الأقسام
            if any(word in line.lower() for word in ['analysis', 'تحليل']):
                current_section = 'system_analysis'
            elif any(word in line.lower() for word in ['issues', 'problems', 'مشاكل']):
                current_section = 'performance_issues'
            elif any(word in line.lower() for word in ['recommendations', 'توصيات']):
                current_section = 'recommendations'
            elif any(word in line.lower() for word in ['priority', 'urgent', 'أولوية']):
                current_section = 'priority_actions'
            elif line.startswith('-') or line.startswith('•') or line.startswith('*'):
                # نقطة في قائمة
                item = line.lstrip('-•*').strip()
                if current_section and current_section in analysis:
                    if isinstance(analysis[current_section], list):
                        analysis[current_section].append(item)
        
        return analysis
    
    def _fallback_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تحليل احتياطي عند عدم توفر Ollama"""
        return {
            'system_analysis': 'تحليل أساسي (Ollama غير متوفر)',
            'performance_issues': ['لا يمكن التحليل المحلي بدون Ollama'],
            'recommendations': [
                'تثبيت Ollama للحصول على تحليل محلي',
                'استخدام الوكلاء الأخرى للتحليل'
            ],
            'priority_actions': ['تثبيت وتشغيل Ollama'],
            'health_score': 50,
            'note': 'هذا تحليل أساسي فقط'
        }
    
    def get_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """استخراج التوصيات من التحليل"""
        if 'error' in analysis:
            return ['❌ خطأ في الحصول على توصيات Ollama']
        
        recommendations = []
        
        # توصيات من Ollama
        if 'recommendations' in analysis:
            recommendations.extend(analysis['recommendations'])
        
        # إجراءات ذات أولوية
        if 'priority_actions' in analysis:
            priority_actions = analysis['priority_actions']
            for action in priority_actions:
                recommendations.append(f"🔥 أولوية: {action}")
        
        # تقييم الصحة
        health_score = analysis.get('health_score', 0)
        if health_score < 50:
            recommendations.append('🚨 النظام يحتاج تدخل فوري!')
        elif health_score < 75:
            recommendations.append('⚠️ النظام يحتاج تحسينات')
        else:
            recommendations.append('✅ النظام في حالة جيدة')
        
        return recommendations or ['لا توجد توصيات محددة']
    
    def ask_ollama(self, question: str) -> str:
        """سؤال مباشر لـ Ollama"""
        if not self.ollama_available:
            return "❌ Ollama غير متوفر"
        
        try:
            prompt = f"""
You are an expert in VS Code and system performance optimization. Answer the following question:

Question: {question}

Provide a detailed and helpful answer in Arabic when possible.
"""
            response = self._call_ollama(prompt)
            return response.strip()
            
        except Exception as e:
            return f"❌ خطأ في السؤال: {e}"
    
    def get_available_models(self) -> List[str]:
        """الحصول على النماذج المتاحة"""
        if not self.ollama_available:
            return []
        
        try:
            # محاولة API أولاً
            response = requests.get(f"{self.api_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
        except:
            pass
        
        try:
            # محاولة CLI
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # تخطي العنوان
                return [line.split()[0] for line in lines if line.strip()]
        except:
            pass
        
        return []
