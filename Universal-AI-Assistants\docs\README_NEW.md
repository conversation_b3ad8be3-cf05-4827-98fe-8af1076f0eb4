# 🏺 نظام أنوبيس للذكاء الاصطناعي
## Anubis AI Assistant System v2.0

![Version](https://img.shields.io/badge/version-2.0-blue.svg)
![Status](https://img.shields.io/badge/status-Production%20Ready-green.svg)
![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![Tests](https://img.shields.io/badge/tests-100%25%20Pass-brightgreen.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

نظام متقدم للذكاء الاصطناعي يضم مجموعة من الوكلاء الذكيين للتطوير الكامل (Full-Stack Development) مع تكامل Gemini CLI

---

## 🌟 المميزات الرئيسية

### 🤖 الوكلاء الذكيين المحسنين (100% نجاح)
- **🔍 ErrorDetectorAgent**: كشف الأخطاء المتقدم للباك إند والفرونت إند
- **📊 ProjectAnalyzerAgent**: تحليل شامل للمشاريع وكشف التقنيات
- **📁 FileOrganizerAgent**: تنظيم ذكي للملفات وإنشاء هياكل المشاريع
- **🧠 MemoryAgent**: إدارة الذاكرة الذكية والبحث المتقدم

### 🚀 التقنيات المدعومة
#### Backend Technologies
- **Python**: Django, FastAPI, Flask
- **JavaScript/Node.js**: Express.js
- **PHP**: Laravel
- **Java**: Spring Boot
- **Go**: Gin, Echo
- **Rust**: Actix, Rocket

#### Frontend Technologies
- **React**: JSX, TSX, Hooks, Next.js
- **Vue.js**: Vue 3, Composition API, Nuxt.js
- **Angular**: TypeScript, Angular CLI
- **Svelte**: SvelteKit

### 💎 التكامل مع Gemini CLI
- تطوير سريع بمساعدة الذكاء الاصطناعي
- إصلاح تلقائي للأخطاء
- اقتراحات ذكية للتحسين
- تكامل آمن مع إدارة العمليات

---

## 📁 هيكل المشروع المنظم

```
Universal-AI-Assistants/
├── 📂 core/                    # الملفات الأساسية للنظام
│   ├── base_agent.py          # الفئة الأساسية للوكلاء
│   ├── ai_integration.py      # تكامل الذكاء الاصطناعي
│   └── assistant_system.py    # نظام المساعد الرئيسي
├── 📂 agents/                  # جميع الوكلاء المحسنة
│   ├── enhanced_error_detector.py      # كاشف الأخطاء المحسن
│   ├── enhanced_project_analyzer.py    # محلل المشاريع المحسن
│   ├── enhanced_file_organizer.py      # منظم الملفات المحسن
│   └── enhanced_memory_agent.py        # وكيل الذاكرة المحسن
├── 📂 tests/                   # ملفات الاختبار الشاملة
│   ├── comprehensive_agents_test.py    # اختبار شامل للوكلاء
│   └── quick_ai_test.py               # اختبار سريع
├── 📂 scripts/                 # سكريبتات التشغيل والإعداد
│   ├── system_paths_manager.py        # مدير المسارات الآمن
│   ├── safe_gemini_integration.py     # تكامل Gemini آمن
│   └── simple_agent_fix.py           # إصلاح سريع للوكلاء
├── 📂 docs/                    # التوثيق والدلائل
│   ├── project_index.json            # فهرس المشروع
│   └── README.md                     # دليل كل مجلد
├── 📂 configs/                 # ملفات التكوين
├── 📂 reports/                 # التقارير والنتائج
├── 📂 examples/                # أمثلة الاستخدام
└── 📂 tools/                   # أدوات مساعدة
```

---

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
```bash
Python 3.8+
pip (Python Package Manager)
Git
Gemini CLI (اختياري للتطوير المتقدم)
```

### خطوات التثبيت السريع

1. **استنساخ المشروع**
```bash
git clone https://github.com/amrashour1/Universal-AI-Assistants.git
cd Universal-AI-Assistants
```

2. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

3. **تشغيل الاختبار الشامل**
```bash
python tests/comprehensive_agents_test.py
```

---

## 📖 دليل الاستخدام السريع

### 1. كشف الأخطاء المتقدم
```python
from agents.enhanced_error_detector import EnhancedErrorDetectorAgent

# إنشاء الوكيل
detector = EnhancedErrorDetectorAgent(".", {}, True)

# فحص ملف واحد
result = detector.scan_single_file("path/to/your/file.py")
print(f"المشاكل المكتشفة: {result['summary']['total_issues']}")

# فحص المشروع كاملاً
project_result = detector.scan_entire_project()
print(f"الملفات المفحوصة: {project_result['summary']['total_files']}")
```

### 2. تحليل المشروع الذكي
```python
from agents.enhanced_project_analyzer import EnhancedProjectAnalyzerAgent

# إنشاء الوكيل
analyzer = EnhancedProjectAnalyzerAgent(".", {}, True)

# تحليل المشروع
result = analyzer.analyze_project()
print(f"نوع المشروع: {result['project_type']}")
print(f"عدد الملفات: {result['files_count']}")
print(f"نقاط الجودة: {result['quality_score']}/100")
print(f"التقنيات المكتشفة: {result['technologies']}")
```

### 3. تنظيم الملفات وإنشاء المشاريع
```python
from agents.enhanced_file_organizer import EnhancedFileOrganizerAgent

# إنشاء الوكيل
organizer = EnhancedFileOrganizerAgent(".", {}, True)

# تنظيم الملفات حسب النوع
result = organizer.organize_files()
print(f"تم تنظيم: {result['organized_files']} ملف")

# إنشاء مشروع React جديد
project = organizer.create_project_structure("react", "my-react-app")
print(f"تم إنشاء: {len(project['created_folders'])} مجلد")

# إنشاء مشروع Django
django_project = organizer.create_project_structure("django", "my-django-app")
```

### 4. إدارة الذاكرة الذكية
```python
from agents.enhanced_memory_agent import EnhancedMemoryAgent

# إنشاء الوكيل
memory = EnhancedMemoryAgent(".", {}, True)

# تخزين معلومات المشروع
memory.store_memory("project_info", {
    "name": "نظام أنوبيس",
    "version": "2.0",
    "agents": 4,
    "status": "production"
}, "projects")

# استرجاع المعلومات
data = memory.retrieve_memory("project_info")
print(f"اسم المشروع: {data['data']['name']}")

# البحث في الذاكرة
results = memory.search_memory("أنوبيس")
print(f"نتائج البحث: {len(results)}")

# إحصائيات الذاكرة
stats = memory.get_memory_stats()
print(f"إجمالي الذكريات: {stats['total_memories']}")
```

---

## 🧪 نتائج الاختبارات الشاملة

### آخر تشغيل للاختبارات (2025-07-16)
```
🏆 نتائج الاختبار الشامل:
   ✅ نجح: 4/4 وكيل
   📊 معدل النجاح: 100.0%

📋 تفاصيل النتائج:
   ✅ error_detector: success (7 مشاكل مكتشفة)
   ✅ project_analyzer: success (1220 ملف محلل)
   ✅ file_organizer: success (4 فئات منظمة)
   ✅ memory_agent: success (تخزين واسترجاع ناجح)
```

### اختبارات الأداء
- **كشف الأخطاء**: 7 مشاكل في ملف اختبار
- **تحليل المشروع**: 1220 ملف في ثوانٍ
- **تنظيم الملفات**: 4 فئات تلقائياً
- **إدارة الذاكرة**: بحث فوري في الذكريات

---

## 🔧 التكوين المتقدم

### إعداد الذكاء الاصطناعي
```json
{
  "ai_enabled": true,
  "provider": "openai",
  "model": "gpt-4",
  "api_key": "your-api-key",
  "timeout": 60
}
```

### إعداد Gemini CLI
```bash
# تثبيت Gemini CLI
npm install -g @google/generative-ai-cli

# إعداد API Key
gemini config set api-key YOUR_API_KEY

# اختبار التكامل
python scripts/safe_gemini_integration.py
```

---

## 📊 إحصائيات المشروع

| المكون | الحالة | الملفات | الاختبارات | الميزات |
|--------|--------|---------|------------|---------|
| ErrorDetector | ✅ مكتمل | 1 | ✅ 100% | متعدد اللغات |
| ProjectAnalyzer | ✅ مكتمل | 1 | ✅ 100% | كشف ذكي |
| FileOrganizer | ✅ مكتمل | 1 | ✅ 100% | إنشاء مشاريع |
| MemoryAgent | ✅ مكتمل | 1 | ✅ 100% | بحث ذكي |
| **المجموع** | **✅ جاهز** | **4** | **100%** | **شامل** |

### إحصائيات التنظيم الأخيرة
- **📁 المجلدات المنشأة**: 12 مجلد
- **📄 الملفات المنقولة**: 13 ملف
- **🔧 الملفات المحدثة**: تحديث تلقائي للمسارات
- **📋 الفهرس**: تم إنشاء فهرس شامل

---

## 🏆 الإنجازات المحققة

- ✅ **4 وكلاء ذكيين محسنين** مع نجاح 100%
- ✅ **دعم 15+ تقنية تطوير** (React, Vue, Django, FastAPI, etc.)
- ✅ **تكامل آمن مع Gemini CLI** للتطوير السريع
- ✅ **اختبارات شاملة** مع تقارير مفصلة
- ✅ **تنظيم مشروع احترافي** مع هيكل واضح
- ✅ **توثيق شامل** باللغتين العربية والإنجليزية
- ✅ **نظام إدارة مسارات آمن** لتجنب أخطاء الترمينال
- ✅ **تقارير تفصيلية** لجميع العمليات

---

## 🔮 الخطط المستقبلية

- [ ] واجهة ويب تفاعلية مع Dashboard
- [ ] دعم المزيد من لغات البرمجة (C++, C#, Kotlin)
- [ ] تكامل مع GitHub Actions للـ CI/CD
- [ ] دعم التطوير السحابي (AWS, Azure, GCP)
- [ ] إضافة وكلاء جديدين (SecurityAgent, PerformanceAgent)
- [ ] دعم الذكاء الاصطناعي المحلي (Ollama)

---

## 🤝 المساهمة والدعم

### كيفية المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### الحصول على الدعم
- **GitHub Issues**: [فتح مشكلة جديدة](https://github.com/amrashour1/Universal-AI-Assistants/issues)
- **المطور**: Amr Ashour
- **البريد الإلكتروني**: <EMAIL>

---

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

<div align="center">

**🏺 نظام أنوبيس - حيث يلتقي الذكاء الاصطناعي بالتطوير الاحترافي**

**Made with ❤️ by Amr Ashour**

[![GitHub](https://img.shields.io/badge/GitHub-amrashour1-blue.svg)](https://github.com/amrashour1)
[![Version](https://img.shields.io/badge/Version-2.0-success.svg)](CHANGELOG.md)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)](README.md)

</div>
