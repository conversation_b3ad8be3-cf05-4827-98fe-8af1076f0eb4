{"project_name": "نظام أنوبيس للذكاء الاصطناعي", "version": "2.0", "last_organized": "2025-07-16T07:49:37.779757", "folder_structure": {"core": {"description": "الملفات الأساسية للنظام", "files": ["README.md"], "count": 1}, "agents": {"description": "جميع الوكلاء المحسنة", "files": ["README.md"], "count": 1}, "tests": {"description": "ملفات الاختبار", "files": ["README.md"], "count": 1}, "scripts": {"description": "سكريبتات التشغيل والإعداد", "files": ["cleanup_and_organize.py", "quick_gemini_fix.py", "quick_start.py", "README.md", "safe_gemini_integration.py", "simple_agent_fix.py", "system_paths_manager.py"], "count": 7}, "configs": {"description": "ملفات التكوين", "files": ["README.md"], "count": 1}, "docs": {"description": "التوثيق والدلائل", "files": ["README.md"], "count": 1}, "reports": {"description": "التقارير والنتائج", "files": ["README.md"], "count": 1}, "logs": {"description": "ملفات السجلات", "files": ["README.md"], "count": 1}, "backup": {"description": "النسخ الاحتياطية", "files": ["README.md"], "count": 1}, "temp": {"description": "الملفات المؤقتة", "files": ["README.md"], "count": 1}, "examples": {"description": "أمثلة الاستخدام", "files": ["README.md"], "count": 1}, "tools": {"description": "أدوات مساعدة", "files": ["README.md"], "count": 1}}, "file_counts": {"core": 1, "agents": 1, "tests": 1, "scripts": 7, "configs": 1, "docs": 1, "reports": 1, "logs": 1, "backup": 1, "temp": 1, "examples": 1, "tools": 1}, "total_files": 18}