{"name": "Anubis Project Analysis Workflow", "nodes": [{"parameters": {}, "id": "1a2b3c4d-5e6f-7g8h-9i0j-k1l2m3n4o5p6", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"prompt": "Analyze this project comprehensively. Create a detailed plan for analysis including:\n1. Code quality assessment\n2. Error detection priorities\n3. File organization recommendations\n4. Performance optimization suggestions\n\nProject path: {{$json.project_path || '.'}}", "role": "commander", "temperature": 0.3, "useInputData": true, "includeMetadata": true}, "id": "2b3c4d5e-6f7g-8h9i-0j1k-l2m3n4o5p6q7", "name": "Gemini Commander", "type": "anubisG<PERSON>ini", "typeVersion": 1, "position": [460, 300], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "error_analysis", "leftValue": "={{$json.response}}", "rightValue": "error", "operator": {"type": "string", "operation": "contains"}}, {"id": "code_analysis", "leftValue": "={{$json.response}}", "rightValue": "code", "operator": {"type": "string", "operation": "contains"}}, {"id": "file_organization", "leftValue": "={{$json.response}}", "rightValue": "file", "operator": {"type": "string", "operation": "contains"}}], "combineOperation": "any"}, "options": {}}, "id": "3c4d5e6f-7g8h-9i0j-1k2l-m3n4o5p6q7r8", "name": "Analysis Router", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [680, 300]}, {"parameters": {"agentType": "error_detector", "projectPath": "={{$json.project_path || '.'}}", "verbose": true, "config": "{\n  \"deep_scan\": true,\n  \"include_warnings\": true,\n  \"check_syntax\": true\n}", "additionalFields": {"timeout": 300, "includePattern": "*.py,*.js,*.ts,*.jsx,*.tsx", "saveToDatabase": true}}, "id": "4d5e6f7g-8h9i-0j1k-2l3m-n4o5p6q7r8s9", "name": "Error Detection Agent", "type": "anubisAgents", "typeVersion": 1, "position": [900, 200], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"agentType": "code_analyzer", "projectPath": "={{$json.project_path || '.'}}", "verbose": true, "config": "{\n  \"analyze_complexity\": true,\n  \"check_patterns\": true,\n  \"security_scan\": true\n}", "additionalFields": {"timeout": 300, "includePattern": "*.py,*.js,*.ts", "saveToDatabase": true}}, "id": "5e6f7g8h-9i0j-1k2l-3m4n-o5p6q7r8s9t0", "name": "Code Analysis Agent", "type": "anubisAgents", "typeVersion": 1, "position": [900, 300], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"agentType": "file_organizer", "projectPath": "={{$json.project_path || '.'}}", "verbose": true, "config": "{\n  \"create_structure\": true,\n  \"clean_duplicates\": true,\n  \"organize_by_type\": true\n}", "additionalFields": {"timeout": 300, "saveToDatabase": true}}, "id": "6f7g8h9i-0j1k-2l3m-4n5o-p6q7r8s9t0u1", "name": "File Organization Agent", "type": "anubisAgents", "typeVersion": 1, "position": [900, 400], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"prompt": "Analyze the code using Llama3 for Arabic documentation and general insights:\n\nProject: {{$json.project_path || '.'}}\nContext: {{$json.response}}", "model": "llama3:8b", "temperature": 0.5, "maxTokens": 2000, "useInputData": true}, "id": "7g8h9i0j-1k2l-3m4n-5o6p-q7r8s9t0u1v2", "name": "Llama3 Analysis", "type": "an<PERSON>s<PERSON><PERSON><PERSON>", "typeVersion": 1, "position": [900, 500], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"prompt": "Provide technical code review and English documentation:\n\nProject: {{$json.project_path || '.'}}\nContext: {{$json.response}}", "model": "mistral:7b", "temperature": 0.3, "maxTokens": 2000, "useInputData": true}, "id": "8h9i0j1k-2l3m-4n5o-6p7q-r8s9t0u1v2w3", "name": "Mistral Code Review", "type": "an<PERSON>s<PERSON><PERSON><PERSON>", "typeVersion": 1, "position": [900, 600], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {}}, "id": "9i0j1k2l-3m4n-5o6p-7q8r-s9t0u1v2w3x4", "name": "Merge Results", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [1120, 350]}, {"parameters": {"prompt": "Review all analysis results and provide final recommendations:\n\n{{JSON.stringify($json, null, 2)}}\n\nProvide:\n1. Executive summary\n2. Priority recommendations\n3. Action plan\n4. Risk assessment", "role": "reviewer", "temperature": 0.2, "useInputData": true, "includeMetadata": true}, "id": "0j1k2l3m-4n5o-6p7q-8r9s-t0u1v2w3x4y5", "name": "Gemini Final Review", "type": "anubisG<PERSON>ini", "typeVersion": 1, "position": [1340, 350], "credentials": {"anubisApi": {"id": "anubis-api-cred", "name": "Anubis API Credentials"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "url": "={{$credentials.anubisApi.apiUrl}}/api/v1/database/projects", "method": "POST", "sendBody": true, "bodyContentType": "json", "jsonBody": "{\n  \"name\": \"Comprehensive Analysis - {{new Date().toISOString()}}\",\n  \"path\": \"{{$json.project_path || '.'}}\",\n  \"type\": \"comprehensive_analysis\",\n  \"description\": \"Full project analysis with multiple models and agents\"\n}", "options": {}}, "id": "1k2l3m4n-5o6p-7q8r-9s0t-u1v2w3x4y5z6", "name": "Save to Database", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 300], "credentials": {"httpHeaderAuth": {"id": "anubis-http-auth", "name": "<PERSON><PERSON><PERSON> H<PERSON>"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "url": "={{$credentials.anubisApi.apiUrl}}/api/v1/langsmith/trace", "method": "POST", "sendBody": true, "bodyContentType": "json", "jsonBody": "{\n  \"name\": \"anubis_comprehensive_analysis\",\n  \"inputs\": {{JSON.stringify($json)}},\n  \"tags\": [\"anubis\", \"analysis\", \"comprehensive\"]\n}", "options": {}}, "id": "2l3m4n5o-6p7q-8r9s-0t1u-v2w3x4y5z6a7", "name": "LangSmith Tracking", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1560, 450], "credentials": {"httpHeaderAuth": {"id": "anubis-http-auth", "name": "<PERSON><PERSON><PERSON> H<PERSON>"}}}], "connections": {"Manual Trigger": {"main": [[{"node": "Gemini Commander", "type": "main", "index": 0}]]}, "Gemini Commander": {"main": [[{"node": "Analysis Router", "type": "main", "index": 0}]]}, "Analysis Router": {"main": [[{"node": "Error Detection Agent", "type": "main", "index": 0}], [{"node": "Code Analysis Agent", "type": "main", "index": 0}], [{"node": "File Organization Agent", "type": "main", "index": 0}]]}, "Error Detection Agent": {"main": [[{"node": "Merge Results", "type": "main", "index": 0}]]}, "Code Analysis Agent": {"main": [[{"node": "Merge Results", "type": "main", "index": 1}]]}, "File Organization Agent": {"main": [[{"node": "Merge Results", "type": "main", "index": 2}]]}, "Llama3 Analysis": {"main": [[{"node": "Merge Results", "type": "main", "index": 3}]]}, "Mistral Code Review": {"main": [[{"node": "Merge Results", "type": "main", "index": 4}]]}, "Merge Results": {"main": [[{"node": "Gemini Final Review", "type": "main", "index": 0}]]}, "Gemini Final Review": {"main": [[{"node": "Save to Database", "type": "main", "index": 0}, {"node": "LangSmith Tracking", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-01-16T09:30:00.000Z", "updatedAt": "2025-01-16T09:30:00.000Z", "id": "anubis-analysis", "name": "Anubis Analysis"}], "triggerCount": 0, "updatedAt": "2025-01-16T09:30:00.000Z", "versionId": "1"}