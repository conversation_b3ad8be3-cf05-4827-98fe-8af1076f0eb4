#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
💎 نظام التكامل الآمن مع Gemini CLI
Safe Gemini CLI Integration System

نظام محسن للتعاون مع Gemini CLI باستخدام مدير المسارات الآمن
"""

import os
import sys
import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# استيراد مدير المسارات
from system_paths_manager import paths_manager


class SafeGeminiIntegration:
    """💎 نظام التكامل الآمن مع Gemini CLI"""
    
    def __init__(self):
        self.paths_manager = paths_manager
        self.conversation_log = []
        self.agents_status = {
            'ErrorDetectorAgent': '✅ مكتمل',
            'ProjectAnalyzerAgent': '🔄 قيد الإصلاح',
            'FileOrganizerAgent': '🔄 قيد الإصلاح',
            'MemoryAgent': '🔄 قيد الإصلاح'
        }
        
        print("💎 نظام التكامل الآمن مع Gemini CLI - نظام أنوبيس")
        print(f"📁 المسار الآمن: {self.paths_manager.get_safe_working_directory()}")
    
    def check_gemini_availability(self) -> bool:
        """فحص توفر Gemini CLI بشكل آمن"""
        print("🔍 فحص توفر Gemini CLI...")
        
        result = self.paths_manager.run_safe_command("gemini --version", timeout=10)
        
        if result['status'] == 'completed' and result['success']:
            print("✅ Gemini CLI متاح ويعمل")
            return True
        else:
            print(f"❌ Gemini CLI غير متاح: {result.get('error', 'خطأ غير معروف')}")
            return False
    
    def ask_gemini_safely(self, prompt: str, timeout: int = 120) -> Dict[str, Any]:
        """طرح سؤال على Gemini CLI بشكل آمن"""
        print(f"💎 إرسال طلب لـ Gemini CLI...")
        
        # تنظيف العمليات القديمة أولاً
        self.paths_manager.cleanup_old_processes()
        
        # تشغيل الأمر
        result = self.paths_manager.run_gemini_command(prompt, timeout=timeout)
        
        # تسجيل المحادثة
        conversation_entry = {
            'timestamp': datetime.now().isoformat(),
            'prompt_preview': prompt[:100] + "..." if len(prompt) > 100 else prompt,
            'result': result,
            'success': result.get('success', False)
        }
        
        self.conversation_log.append(conversation_entry)
        
        if result['status'] == 'completed' and result['success']:
            print(f"✅ تم الحصول على استجابة من Gemini")
            return {
                'success': True,
                'response': result['stdout'],
                'process_id': result['process_id']
            }
        else:
            print(f"❌ فشل في الحصول على استجابة: {result.get('error', 'خطأ غير معروف')}")
            return {
                'success': False,
                'error': result.get('error', 'خطأ غير معروف'),
                'process_id': result.get('process_id')
            }
    
    def fix_project_analyzer_safely(self) -> str:
        """إصلاح ProjectAnalyzerAgent بشكل آمن"""
        print("\n📊 إصلاح ProjectAnalyzerAgent بشكل آمن...")
        
        prompt = """
        أحتاج إنشاء ProjectAnalyzerAgent محسن لنظام أنوبيس.
        
        المتطلبات:
        1. وراثة من BaseAgent
        2. دعم الذكاء الاصطناعي
        3. تحليل شامل للمشاريع (React, Vue, Django, FastAPI, Next.js)
        4. كشف نوع المشروع تلقائياً
        5. تحليل جودة الكود والأداء
        6. توصيات ذكية للتحسين
        
        أنشئ الكود كاملاً بدءاً من:
        #!/usr/bin/env python3
        # -*- coding: utf-8 -*-
        
        يجب أن يحتوي على:
        - class EnhancedProjectAnalyzerAgent(BaseAgent)
        - def get_agent_type(self) -> str
        - def initialize_agent(self)
        - def analyze_project(self, project_path=None)
        - دعم كامل للتطوير الكامل (باك إند + فرونت إند)
        
        أريد الكود كاملاً جاهزاً للتشغيل!
        """
        
        gemini_result = self.ask_gemini_safely(prompt, timeout=180)
        
        if gemini_result['success']:
            code = self._extract_code_from_response(gemini_result['response'])
            
            if code:
                # حفظ الكود بشكل آمن
                agent_file = self.paths_manager.create_agent_file_safely(
                    'enhanced_project_analyzer', 
                    code
                )
                
                if agent_file:
                    self.agents_status['ProjectAnalyzerAgent'] = '✅ مكتمل'
                    print(f"✅ تم إنشاء ProjectAnalyzerAgent: {agent_file}")
                    return agent_file
                else:
                    print("❌ فشل في حفظ الملف")
                    return None
            else:
                print("❌ لم يتم العثور على كود في الاستجابة")
                return None
        else:
            print(f"❌ فشل في الحصول على كود من Gemini: {gemini_result['error']}")
            return None
    
    def fix_file_organizer_safely(self) -> str:
        """إصلاح FileOrganizerAgent بشكل آمن"""
        print("\n📁 إصلاح FileOrganizerAgent بشكل آمن...")
        
        prompt = """
        أحتاج إنشاء FileOrganizerAgent محسن لنظام أنوبيس.
        
        المتطلبات:
        1. وراثة من BaseAgent
        2. دعم الذكاء الاصطناعي
        3. تنظيم ذكي للملفات حسب نوع المشروع
        4. إنشاء هياكل مشاريع جديدة (React, Vue, Django, FastAPI)
        5. تنظيم ملفات Docker, CI/CD
        6. اقتراحات تنظيم ذكية
        
        أنشئ الكود كاملاً بدءاً من:
        #!/usr/bin/env python3
        # -*- coding: utf-8 -*-
        
        يجب أن يحتوي على:
        - class EnhancedFileOrganizerAgent(BaseAgent)
        - def get_agent_type(self) -> str
        - def initialize_agent(self)
        - def organize_files(self, target_path=None)
        - def create_project_structure(self, project_type, project_name)
        
        أريد الكود كاملاً جاهزاً للتشغيل!
        """
        
        gemini_result = self.ask_gemini_safely(prompt, timeout=180)
        
        if gemini_result['success']:
            code = self._extract_code_from_response(gemini_result['response'])
            
            if code:
                agent_file = self.paths_manager.create_agent_file_safely(
                    'enhanced_file_organizer', 
                    code
                )
                
                if agent_file:
                    self.agents_status['FileOrganizerAgent'] = '✅ مكتمل'
                    print(f"✅ تم إنشاء FileOrganizerAgent: {agent_file}")
                    return agent_file
                else:
                    print("❌ فشل في حفظ الملف")
                    return None
            else:
                print("❌ لم يتم العثور على كود في الاستجابة")
                return None
        else:
            print(f"❌ فشل في الحصول على كود من Gemini: {gemini_result['error']}")
            return None
    
    def fix_memory_agent_safely(self) -> str:
        """إصلاح MemoryAgent بشكل آمن"""
        print("\n🧠 إصلاح MemoryAgent بشكل آمن...")
        
        prompt = """
        أحتاج إنشاء MemoryAgent محسن لنظام أنوبيس.
        
        المتطلبات:
        1. وراثة من BaseAgent
        2. دعم الذكاء الاصطناعي
        3. دوال كاملة للتخزين والاسترجاع (store_memory, retrieve_memory)
        4. دعم قواعد البيانات المختلفة
        5. بحث ذكي في الذاكرة
        6. تخزين معلومات المشاريع والكود
        
        أنشئ الكود كاملاً بدءاً من:
        #!/usr/bin/env python3
        # -*- coding: utf-8 -*-
        
        يجب أن يحتوي على:
        - class EnhancedMemoryAgent(BaseAgent)
        - def get_agent_type(self) -> str
        - def initialize_agent(self)
        - def store_memory(self, key, data)
        - def retrieve_memory(self, key)
        - def search_memory(self, query)
        
        أريد الكود كاملاً جاهزاً للتشغيل!
        """
        
        gemini_result = self.ask_gemini_safely(prompt, timeout=180)
        
        if gemini_result['success']:
            code = self._extract_code_from_response(gemini_result['response'])
            
            if code:
                agent_file = self.paths_manager.create_agent_file_safely(
                    'enhanced_memory_agent', 
                    code
                )
                
                if agent_file:
                    self.agents_status['MemoryAgent'] = '✅ مكتمل'
                    print(f"✅ تم إنشاء MemoryAgent: {agent_file}")
                    return agent_file
                else:
                    print("❌ فشل في حفظ الملف")
                    return None
            else:
                print("❌ لم يتم العثور على كود في الاستجابة")
                return None
        else:
            print(f"❌ فشل في الحصول على كود من Gemini: {gemini_result['error']}")
            return None
    
    def _extract_code_from_response(self, response: str) -> str:
        """استخراج الكود من استجابة Gemini"""
        if not response:
            return None
        
        lines = response.split('\n')
        code_lines = []
        in_code_block = False
        
        for line in lines:
            # البحث عن بداية الكود
            if line.strip().startswith('```python') or line.strip().startswith('#!/usr/bin/env python3'):
                in_code_block = True
                if line.strip().startswith('#!/usr/bin/env python3'):
                    code_lines.append(line)
                continue
            elif line.strip() == '```' and in_code_block:
                in_code_block = False
                continue
            elif in_code_block:
                code_lines.append(line)
            elif line.strip().startswith('#!/usr/bin/env python3'):
                # بداية كود مباشر
                code_lines.append(line)
                in_code_block = True
        
        if code_lines:
            return '\n'.join(code_lines)
        
        # البحث عن أي كود Python
        for i, line in enumerate(lines):
            if 'class ' in line and 'Agent' in line:
                return '\n'.join(lines[i-1:]) if i > 0 else '\n'.join(lines[i:])
        
        return None
    
    def fix_all_agents_safely(self) -> Dict[str, str]:
        """إصلاح جميع الوكلاء بشكل آمن"""
        print("🚀 إصلاح آمن لجميع الوكلاء مع Gemini CLI")
        print("=" * 60)
        
        # فحص Gemini أولاً
        if not self.check_gemini_availability():
            print("❌ لا يمكن المتابعة بدون Gemini CLI")
            return {}
        
        results = {}
        
        # إصلاح الوكلاء واحداً تلو الآخر
        agents_to_fix = [
            ('ProjectAnalyzerAgent', self.fix_project_analyzer_safely),
            ('FileOrganizerAgent', self.fix_file_organizer_safely),
            ('MemoryAgent', self.fix_memory_agent_safely)
        ]
        
        for agent_name, fix_function in agents_to_fix:
            try:
                print(f"\n🔧 إصلاح {agent_name}...")
                
                agent_file = fix_function()
                
                if agent_file:
                    results[agent_name] = agent_file
                    print(f"✅ {agent_name}: نجح الإصلاح")
                    
                    # انتظار قصير بين الوكلاء
                    time.sleep(2)
                else:
                    print(f"❌ {agent_name}: فشل الإصلاح")
                    
            except Exception as e:
                print(f"❌ خطأ في إصلاح {agent_name}: {e}")
        
        return results
    
    def generate_final_report(self, results: Dict[str, str]) -> str:
        """إنتاج تقرير نهائي"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'integration_type': 'safe_gemini_integration',
            'agents_status': self.agents_status,
            'fixed_agents': results,
            'conversation_log': self.conversation_log,
            'system_status': self.paths_manager.get_system_status(),
            'summary': {
                'total_agents': len(self.agents_status),
                'completed_agents': len([s for s in self.agents_status.values() if '✅' in s]),
                'fixed_in_session': len(results),
                'success_rate': len(results) / 3 * 100  # 3 وكلاء للإصلاح
            }
        }
        
        # حفظ التقرير
        report_file = self.paths_manager.get_path('reports') / f"safe_gemini_integration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📄 تم حفظ التقرير النهائي: {report_file}")
            return str(report_file)
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")
            return None


def main():
    """الدالة الرئيسية للتكامل الآمن"""
    print("💎 نظام التكامل الآمن مع Gemini CLI - نظام أنوبيس")
    print("=" * 60)
    
    # إنشاء نظام التكامل الآمن
    integration = SafeGeminiIntegration()
    
    # إصلاح جميع الوكلاء
    results = integration.fix_all_agents_safely()
    
    # عرض النتائج
    print("\n🏆 نتائج الإصلاح الآمن:")
    for agent_name, file_path in results.items():
        print(f"   ✅ {agent_name}: {file_path}")
    
    # عرض حالة الوكلاء
    print(f"\n📊 حالة جميع الوكلاء:")
    for agent_name, status in integration.agents_status.items():
        print(f"   {status} {agent_name}")
    
    # إنتاج التقرير النهائي
    report_file = integration.generate_final_report(results)
    
    # تنظيف العمليات
    integration.paths_manager.cleanup_old_processes()
    
    print(f"\n💎 تم إصلاح {len(results)} وكيل بنجاح!")
    print("🚀 الخطوة التالية: اختبار الوكلاء المحسنة")
    
    return 0 if len(results) > 0 else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
