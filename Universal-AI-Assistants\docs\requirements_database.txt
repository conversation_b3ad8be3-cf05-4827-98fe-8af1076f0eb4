# 🏺 متطلبات قاعدة بيانات نظام أنوبيس
# Anubis AI Assistants System - Database Requirements

# MySQL Database Connector
mysql-connector-python==8.2.0

# Alternative MySQL drivers (optional)
PyMySQL==1.1.0
mysqlclient==2.2.0

# Database utilities
SQLAlchemy==2.0.23
alembic==1.12.1

# Connection pooling and management
DBUtils==3.0.3

# Data validation and serialization
pydantic==2.5.0
marshmallow==3.20.1

# JSON handling
ujson==5.8.0

# Async database support (optional)
aiomysql==0.2.0
asyncio-pool==0.6.0

# Database migration tools
yoyo-migrations==8.2.0

# Performance monitoring
py-spy==0.3.14

# Security
cryptography==41.0.7
bcrypt==4.1.2

# Logging and monitoring
structlog==23.2.0
python-json-logger==2.0.7

# Configuration management
python-dotenv==1.0.0
configparser==6.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mysql==2.3.0

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1
