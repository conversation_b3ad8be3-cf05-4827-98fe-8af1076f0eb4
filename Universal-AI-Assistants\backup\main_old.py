#!/usr/bin/env python3
"""
🚀 نظام المساعدين الذكيين العالمي - النقطة الرئيسية
Universal AI Assistants System - Main Entry Point

تم تطويره من دمج وتعميم نظام Collaborative_Workspace و agents
"""

import os
import sys
import argparse
import json
import traceback
from pathlib import Path
from datetime import datetime

# إضافة مجلد core إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))


class SystemInitializationError(Exception):
    """استثناء مخصص لأخطاء تهيئة النظام"""
    pass


try:
    from core.assistant_system import UniversalAssistantSystem
    from core.config_manager import ConfigManager
except ImportError as e:
    print(f"❌ خطأ في تحميل النواة الأساسية: {e}")
    print("🔧 تأكد من وجود ملفات النواة في مجلد core/")
    sys.exit(1)


def print_banner():
    """عرض شعار النظام"""
    banner = """
🤖 ═══════════════════════════════════════════════════════════════
   نظام المساعدين الذكيين العالمي
   Universal AI Assistants System v1.0
   
   🎯 نظام متطور للمساعدين الذكيين يعمل مع أي مشروع
   🚀 تم تطويره من دمج Collaborative_Workspace و agents
═══════════════════════════════════════════════════════════════ 🤖
"""
    print(banner)

def parse_arguments():
    """تحليل المعاملات من سطر الأوامر"""
    parser = argparse.ArgumentParser(
        description="نظام المساعدين الذكيين العالمي",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
أمثلة الاستخدام:
  python main.py --project "path/to/project"                    # تحليل شامل
  python main.py --agent database --project "path/to/project"  # وكيل محدد
  python main.py --organize --project "path/to/project"        # تنظيم الملفات
  python main.py --health-check --project "path/to/project"    # فحص الصحة
        """
    )
    
    # المعاملات الأساسية
    parser.add_argument(
        '--project', '-p',
        type=str,
        required=True,
        help='مسار المشروع المراد تحليله'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='configs/default_config.json',
        help='ملف التكوين (افتراضي: configs/default_config.json)'
    )
    
    # أنواع العمليات
    parser.add_argument(
        '--agent', '-a',
        type=str,
        choices=['database', 'organizer', 'memory', 'error', 'analyzer', 'all'],
        default='all',
        help='الوكيل المراد تشغيله (افتراضي: all)'
    )
    
    parser.add_argument(
        '--analyze',
        action='store_true',
        help='تحليل شامل للمشروع'
    )
    
    parser.add_argument(
        '--organize',
        action='store_true',
        help='تنظيم ملفات المشروع'
    )
    
    parser.add_argument(
        '--health-check',
        action='store_true',
        help='فحص صحة المشروع'
    )
    
    parser.add_argument(
        '--fix-issues',
        action='store_true',
        help='إصلاح المشاكل المكتشفة'
    )
    
    # خيارات إضافية
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='عرض تفاصيل أكثر'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        default='workspace/reports',
        help='مجلد حفظ التقارير (افتراضي: workspace/reports)'
    )
    
    parser.add_argument(
        '--project-type',
        type=str,
        choices=['streamlit', 'django', 'fastapi', 'flask', 'custom'],
        help='نوع المشروع (سيتم اكتشافه تلقائياً إذا لم يُحدد)'
    )
    
    return parser.parse_args()

def validate_project_path(project_path):
    """التحقق من صحة مسار المشروع"""
    path = Path(project_path)
    
    if not path.exists():
        print(f"❌ خطأ: المسار غير موجود: {project_path}")
        return False
    
    if not path.is_dir():
        print(f"❌ خطأ: المسار ليس مجلد: {project_path}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # تحليل المعاملات
    args = parse_arguments()
    
    # التحقق من مسار المشروع
    if not validate_project_path(args.project):
        sys.exit(1)
    
    # إنشاء مجلدات العمل
    workspace_dir = Path("workspace")
    workspace_dir.mkdir(exist_ok=True)
    
    reports_dir = Path(args.output)
    reports_dir.mkdir(parents=True, exist_ok=True)
    
    # تهيئة النظام
    try:
        print("🔧 تهيئة نظام المساعدين...")
        
        # تحميل التكوين
        config_manager = ConfigManager(args.config)
        config = config_manager.load_config()
        
        # تهيئة النظام
        system = UniversalAssistantSystem(
            project_path=args.project,
            config=config,
            verbose=args.verbose
        )
        
        print(f"✅ تم تحميل المشروع: {args.project}")
        print(f"📊 نوع المشروع: {system.project_type}")
        print(f"🤖 الوكلاء المفعلين: {len(system.active_agents)}")
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    # تنفيذ العمليات المطلوبة
    try:
        results = {}
        
        if args.analyze or args.agent == 'all':
            print("\n🔍 بدء التحليل الشامل...")
            results['analysis'] = system.analyze_project()
            print("✅ تم التحليل الشامل")
        
        if args.organize:
            print("\n📁 بدء تنظيم الملفات...")
            results['organization'] = system.organize_files()
            print("✅ تم تنظيم الملفات")
        
        if args.health_check:
            print("\n🏥 بدء فحص الصحة...")
            results['health'] = system.health_check()
            print("✅ تم فحص الصحة")
        
        if args.fix_issues:
            print("\n🔧 بدء إصلاح المشاكل...")
            results['fixes'] = system.fix_issues()
            print("✅ تم إصلاح المشاكل")
        
        # تشغيل وكيل محدد
        if args.agent != 'all' and not any([args.analyze, args.organize, args.health_check, args.fix_issues]):
            print(f"\n🤖 تشغيل الوكيل: {args.agent}")
            results[args.agent] = system.run_agent(args.agent)
            print(f"✅ تم تشغيل الوكيل: {args.agent}")
        
        # حفظ التقارير
        if results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"assistant_report_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"\n📋 تم حفظ التقرير: {report_file}")
        
        # عرض الملخص
        print("\n" + "="*60)
        print("🎯 ملخص العمليات:")
        for operation, result in results.items():
            status = "✅ نجح" if result.get('success', True) else "❌ فشل"
            print(f"   {operation}: {status}")
        
        print("\n🚀 تم إنجاز جميع العمليات بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ أثناء التنفيذ: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
