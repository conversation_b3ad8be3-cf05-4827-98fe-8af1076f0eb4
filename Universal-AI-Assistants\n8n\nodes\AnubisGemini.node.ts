import {
	IExecuteFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	NodeOperationError,
} from 'n8n-workflow';

export class <PERSON><PERSON>s<PERSON><PERSON>ini implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Anubis Gemini',
		name: 'anubis<PERSON><PERSON><PERSON>',
		icon: 'file:anubis.svg',
		group: ['anubis'],
		version: 1,
		subtitle: 'Gemini Commander',
		description: 'Interact with Google Gemini through Anubis API',
		defaults: {
			name: 'Anubis Gemini',
		},
		inputs: ['main'],
		outputs: ['main'],
		credentials: [
			{
				name: 'anubisApi',
				required: true,
			},
		],
		properties: [
			{
				displayName: 'Prompt',
				name: 'prompt',
				type: 'string',
				typeOptions: {
					rows: 4,
				},
				default: '',
				placeholder: 'Enter your prompt for Gemini...',
				description: 'The prompt to send to Gemini',
			},
			{
				displayName: 'Temperature',
				name: 'temperature',
				type: 'number',
				default: 0.7,
				typeOptions: {
					minValue: 0,
					maxValue: 1,
					numberStepSize: 0.1,
				},
				description: 'Controls randomness in the response (0 = deterministic, 1 = very random)',
			},
			{
				displayName: 'Role',
				name: 'role',
				type: 'options',
				options: [
					{
						name: 'Commander',
						value: 'commander',
						description: 'Act as the main coordinator and decision maker',
					},
					{
						name: 'Analyst',
						value: 'analyst',
						description: 'Provide detailed analysis and insights',
					},
					{
						name: 'Reviewer',
						value: 'reviewer',
						description: 'Review and validate results from other models',
					},
					{
						name: 'Planner',
						value: 'planner',
						description: 'Create plans and strategies',
					},
				],
				default: 'commander',
				description: 'The role Gemini should play in the workflow',
			},
			{
				displayName: 'Use Input Data',
				name: 'useInputData',
				type: 'boolean',
				default: true,
				description: 'Whether to use data from previous node as context',
			},
			{
				displayName: 'Include Metadata',
				name: 'includeMetadata',
				type: 'boolean',
				default: true,
				description: 'Whether to include execution metadata in the response',
			},
		],
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const returnData: INodeExecutionData[] = [];

		for (let i = 0; i < items.length; i++) {
			try {
				let prompt = this.getNodeParameter('prompt', i) as string;
				const temperature = this.getNodeParameter('temperature', i) as number;
				const role = this.getNodeParameter('role', i) as string;
				const useInputData = this.getNodeParameter('useInputData', i) as boolean;
				const includeMetadata = this.getNodeParameter('includeMetadata', i) as boolean;

				// إضافة السياق حسب الدور
				const roleContext = this.getRoleContext(role);
				prompt = `${roleContext}\n\n${prompt}`;

				// إضافة بيانات الإدخال إلى الـ prompt إذا كان مطلوباً
				if (useInputData && items[i].json) {
					const inputContext = JSON.stringify(items[i].json, null, 2);
					prompt = `Previous workflow data:\n${inputContext}\n\n${prompt}`;
				}

				// إعداد طلب API
				const requestBody = {
					prompt,
					temperature,
				};

				// استدعاء Anubis API
				const response = await this.helpers.httpRequestWithAuthentication.call(
					this,
					'anubisApi',
					{
						method: 'POST',
						url: '/api/v1/models/gemini/generate',
						body: requestBody,
						json: true,
					},
				);

				// إعداد البيانات المرجعة
				const resultData: any = {
					role,
					prompt: response.prompt,
					response: response.response,
					responseTime: response.response_time,
					status: response.status,
					timestamp: new Date().toISOString(),
				};

				// إضافة metadata إذا كان مطلوباً
				if (includeMetadata) {
					resultData.metadata = {
						nodeId: this.getNode().id,
						workflowId: this.getWorkflow().id,
						executionId: this.getExecutionId(),
						itemIndex: i,
					};
				}

				returnData.push({
					json: resultData,
				});

			} catch (error) {
				if (this.continueOnFail()) {
					returnData.push({
						json: {
							error: error.message,
							status: 'error',
							timestamp: new Date().toISOString(),
						},
					});
					continue;
				}
				throw new NodeOperationError(this.getNode(), error);
			}
		}

		return [returnData];
	}

	private getRoleContext(role: string): string {
		const contexts = {
			commander: `You are the Anubis AI Commander. Your role is to:
- Coordinate and manage the workflow
- Make strategic decisions
- Provide clear direction and priorities
- Synthesize information from multiple sources
- Ensure quality and consistency`,

			analyst: `You are the Anubis AI Analyst. Your role is to:
- Provide detailed analysis and insights
- Break down complex problems
- Identify patterns and trends
- Generate comprehensive reports
- Support decision-making with data`,

			reviewer: `You are the Anubis AI Reviewer. Your role is to:
- Review and validate results from other models
- Check for accuracy and consistency
- Identify potential issues or improvements
- Provide quality assurance
- Ensure standards compliance`,

			planner: `You are the Anubis AI Planner. Your role is to:
- Create detailed plans and strategies
- Define workflows and processes
- Set milestones and timelines
- Identify resources and requirements
- Optimize efficiency and effectiveness`,
		};

		return contexts[role] || contexts.commander;
	}
}
