{"project_name": "نظام أنوبيس للذكاء الاصطناعي", "version": "2.0", "last_organized": "2025-07-16T07:44:47.249907", "folder_structure": {"core": {"description": "الملفات الأساسية للنظام", "files": ["ai_integration.py", "assistant_system.py", "base_agent.py", "config_manager.py", "logger.py", "README.md", "__init__.py"], "count": 7}, "agents": {"description": "جميع الوكلاء المحسنة", "files": ["database_agent.py", "enhanced_error_detector.py", "enhanced_file_organizer.py", "enhanced_memory_agent.py", "enhanced_project_analyzer.py", "error_detector.py", "error_detector_agent.py", "file_organizer_agent.py", "memory_agent.py", "project_analyzer_agent.py", "README.md", "smart_ai_agent.py", "smart_code_analyzer.py", "__init__.py"], "count": 14}, "tests": {"description": "ملفات الاختبار", "files": ["ask_anubis.py", "comprehensive_agents_test.py", "comprehensive_system_test.py", "quick_ai_test.py", "README.md", "run_all_tests.py", "test_agents.py", "test_anubis_system.py", "test_error_detector.py", "test_jewelry_database.py", "test_jewelry_logic.py", "test_plugins.py", "test_project_analyzer.py", "test_system.py", "__init__.py"], "count": 15}, "scripts": {"description": "سكريبتات التشغيل والإعداد", "files": ["cleanup_and_organize.py", "quick_gemini_fix.py", "quick_start.py", "README.md", "safe_gemini_integration.py", "simple_agent_fix.py", "system_paths_manager.py", "__init__.py"], "count": 8}, "configs": {"description": "ملفات التكوين", "files": ["ai_config.json", "database_config.json", "default_config.json", "memory.json", "README.md", "system_paths.json"], "count": 6}, "docs": {"description": "التوثيق والدلائل", "files": ["developer_guide.md", "installation_guide.md", "README.md", "user_guide.md"], "count": 4}, "reports": {"description": "التقارير والنتائج", "files": ["ai_integration_test_report_20250716_011209.json", "comprehensive_system_test_report_20250716_064902.json", "comprehensive_test_report_20250716_074112.json", "ollama_check_report_20250716_010906.json", "README.md"], "count": 5}, "logs": {"description": "ملفات السجلات", "files": ["README.md"], "count": 1}, "backup": {"description": "النسخ الاحتياطية", "files": ["README.md"], "count": 1}, "temp": {"description": "الملفات المؤقتة", "files": ["README.md"], "count": 1}, "examples": {"description": "أمثلة الاستخدام", "files": ["README.md"], "count": 1}, "tools": {"description": "أدوات مساعدة", "files": ["README.md"], "count": 1}}, "file_counts": {"core": 7, "agents": 14, "tests": 15, "scripts": 8, "configs": 6, "docs": 4, "reports": 5, "logs": 1, "backup": 1, "temp": 1, "examples": 1, "tools": 1}, "total_files": 64}