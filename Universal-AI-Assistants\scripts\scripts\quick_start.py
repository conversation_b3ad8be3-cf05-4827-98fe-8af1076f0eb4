#!/usr/bin/env python3
"""
🚀 بدء سريع لنظام المساعدين الذكيين العالمي
Quick Start for Universal AI Assistants System

استخدام سريع وسهل للنظام مع مشروع Crestal Diamond
"""

import os
import sys
from pathlib import Path

# إضافة مجلد المشروع إلى المسار
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def main():
    """البدء السريع"""
    print("🚀 ═══════════════════════════════════════════════════════════════")
    print("   بدء سريع - نظام المساعدين الذكيين العالمي")
    print("   Quick Start - Universal AI Assistants System")
    print("═══════════════════════════════════════════════════════════════ 🚀")
    
    # البحث عن مشروع Crestal Diamond
    crestal_path = Path("../Crestal Diamond")
    
    if crestal_path.exists():
        print(f"✅ تم العثور على مشروع Crestal Diamond: {crestal_path}")
        
        # تشغيل النظام مع Crestal Diamond
        print("\n🤖 تشغيل النظام مع مشروع Crestal Diamond...")
        
        cmd = f'python ../main.py --project "{crestal_path}" --analyze --verbose'
        print(f"📝 الأمر: {cmd}")
        
        os.system(cmd)
        
    else:
        print("⚠️ مشروع Crestal Diamond غير موجود")
        print("🔍 يرجى تحديد مسار المشروع يدوياً:")
        print("\n📋 أمثلة الاستخدام:")
        print('   python main.py --project "path/to/your/project" --analyze')
        print('   python main.py --project "path/to/your/project" --organize')
        print('   python main.py --project "path/to/your/project" --health-check')
        
        print("\n🧪 أو تشغيل الاختبارات:")
        print("   python test_system.py")

if __name__ == "__main__":
    main()
