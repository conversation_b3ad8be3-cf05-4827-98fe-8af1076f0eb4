@echo off
chcp 65001 >nul
color 0C
title تعطيل الإضافات الثقيلة في VS Code

echo.
echo ================================================================
echo                    تعطيل الإضافات الثقيلة                    
echo ================================================================
echo.

echo 🔌 تعطيل الإضافات الثقيلة في VS Code...
echo.

REM الإضافات الثقيلة التي يجب تعطيلها
echo 🔴 الإضافات المرشحة للتعطيل:
echo.
echo 1. SonarQube for IDE (423 MB)
echo 2. VS Code Speech (257 MB)  
echo 3. C# (405 MB)
echo 4. PowerShell (301 MB)
echo 5. IntelliPHP - AI Autocomplete (161 MB)
echo 6. CodeLLDB (149 MB)
echo 7. Ionide for F# (137 MB)
echo.

echo 💡 لتعطيل هذه الإضافات:
echo.
echo 1. افتح VS Code
echo 2. اضغط Ctrl+Shift+X
echo 3. ابحث عن كل إضافة من القائمة أعلاه
echo 4. انقر على الإضافة ثم "Disable"
echo 5. أعد تحميل النافذة: Ctrl+Shift+P → "Developer: Reload Window"
echo.

echo 🎯 أو استخدم الأوامر التالية في VS Code Terminal:
echo.

REM أوامر تعطيل الإضافات
echo code --disable-extension SonarSource.sonarlint-vscode
echo code --disable-extension ms-vscode.vscode-speech
echo code --disable-extension ms-dotnettools.csharp
echo code --disable-extension ms-vscode.powershell
echo code --disable-extension intelliphp.intelliphp
echo code --disable-extension vadimcn.vscode-lldb
echo code --disable-extension Ionide.Ionide-fsharp

echo.
echo ⚠️ ملاحظة: تأكد من أنك لا تحتاج هذه الإضافات قبل تعطيلها
echo.

pause
