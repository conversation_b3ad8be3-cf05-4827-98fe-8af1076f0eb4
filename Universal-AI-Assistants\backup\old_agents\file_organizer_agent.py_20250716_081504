#!/usr/bin/env python3
"""
📁 وكيل تنظيم الملفات الذكي العالمي
Universal Intelligent File Organizer Agent

تم تطويره من وكيل تنظيم الملفات في مشروع Crestal Diamond
وتعميمه ليعمل مع أي مشروع
"""

import os
import shutil
import json
import fnmatch
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# إضافة مجلد core إلى المسار
import sys
core_path = os.path.join(os.path.dirname(__file__), '..', 'core')
if core_path not in sys.path:
    sys.path.append(core_path)

try:
    from base_agent import BaseAgent
except ImportError:
    # محاولة استيراد من مسار مختلف
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from core.base_agent import BaseAgent

class FileOrganizerAgent(BaseAgent):
    """وكيل ذكي مسؤول عن تنظيم وترتيب الملفات"""
    
    def get_agent_type(self) -> str:
        """إرجاع نوع الوكيل"""
        return "file_organizer"
    
    def initialize_agent(self):
        """تهيئة وكيل تنظيم الملفات"""
        # قواعد التنظيم من التكوين أو الافتراضية
        self.organization_rules = self.config.get('organization_rules', self._get_default_rules())
        
        # إعدادات التنظيم
        self.auto_organize = self.config.get('auto_organize', False)
        self.backup_before_organize = self.config.get('backup_before_organize', True)
        self.dry_run = self.config.get('dry_run', False)  # تشغيل تجريبي
        
        # مجلدات خاصة
        self.backup_dir = self.workspace_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
        
        self.log_action("تم تهيئة وكيل تنظيم الملفات")
    
    def _get_default_rules(self) -> Dict[str, Any]:
        """قواعد التنظيم الافتراضية"""
        return {
            "core_files": {
                "target_dir": "",
                "patterns": ["main.py", "app.py", "requirements.txt", "README.md", "setup.py"],
                "description": "الملفات الأساسية للمشروع"
            },
            "source_code": {
                "target_dir": "src",
                "patterns": ["*.py"],
                "exclude": ["main.py", "app.py", "setup.py", "test_*.py", "*_test.py"],
                "description": "ملفات الكود المصدري"
            },
            "tests": {
                "target_dir": "tests",
                "patterns": ["test_*.py", "*_test.py", "conftest.py"],
                "extensions": [".py"],
                "description": "ملفات الاختبارات"
            },
            "documentation": {
                "target_dir": "docs",
                "patterns": ["*.md", "*.txt", "*.rst"],
                "exclude": ["README.md"],
                "description": "الوثائق والتقارير"
            },
            "configuration": {
                "target_dir": "config",
                "patterns": ["*.json", "*.yaml", "*.yml", "*.ini", "*.cfg", "*.toml"],
                "description": "ملفات التكوين"
            },
            "data": {
                "target_dir": "data",
                "patterns": ["*.csv", "*.json", "*.xlsx", "*.xml", "*.db", "*.sqlite"],
                "description": "ملفات البيانات"
            },
            "static": {
                "target_dir": "static",
                "patterns": ["*.css", "*.js", "*.html", "*.png", "*.jpg", "*.svg", "*.ico"],
                "description": "الملفات الثابتة"
            },
            "logs": {
                "target_dir": "logs",
                "patterns": ["*.log"],
                "description": "ملفات السجلات"
            },
            "temp": {
                "target_dir": "temp",
                "patterns": ["*.tmp", "*.cache", "*.pyc", "__pycache__"],
                "description": "الملفات المؤقتة"
            },
            "archive": {
                "target_dir": "archive",
                "patterns": ["*backup*", "*old*", "*archive*"],
                "description": "الملفات المؤرشفة"
            }
        }
    
    def run_analysis(self) -> Dict[str, Any]:
        """تشغيل تحليل تنظيم الملفات"""
        analysis_result = {
            'project_structure': {},
            'organization_plan': {},
            'file_statistics': {},
            'recommendations': []
        }
        
        try:
            # تحليل الهيكل الحالي
            analysis_result['project_structure'] = self.analyze_current_structure()
            
            # إنشاء خطة التنظيم
            analysis_result['organization_plan'] = self.create_organization_plan()
            
            # إحصائيات الملفات
            analysis_result['file_statistics'] = self.get_file_statistics()
            
            # إنشاء التوصيات
            analysis_result['recommendations'] = self.generate_organization_recommendations()
            
            # تنفيذ التنظيم إذا كان مفعل
            if self.auto_organize:
                analysis_result['organization_result'] = self.organize_files()
            
        except Exception as e:
            analysis_result['error'] = str(e)
            self.log_action("خطأ في تحليل تنظيم الملفات", str(e))
        
        return analysis_result
    
    def analyze_current_structure(self) -> Dict[str, Any]:
        """تحليل الهيكل الحالي للمشروع"""
        structure = {
            'total_files': 0,
            'total_directories': 0,
            'file_types': {},
            'directory_structure': {},
            'unorganized_files': []
        }
        
        try:
            for item in self.project_path.rglob('*'):
                if item.is_file():
                    structure['total_files'] += 1
                    
                    # تصنيف حسب النوع
                    file_ext = item.suffix.lower()
                    if file_ext not in structure['file_types']:
                        structure['file_types'][file_ext] = 0
                    structure['file_types'][file_ext] += 1
                    
                    # فحص الملفات غير المنظمة
                    if not self._is_file_organized(item):
                        structure['unorganized_files'].append(str(item.relative_to(self.project_path)))
                
                elif item.is_dir():
                    structure['total_directories'] += 1
            
            # تحليل هيكل المجلدات
            structure['directory_structure'] = self._analyze_directory_structure()
            
        except Exception as e:
            structure['error'] = str(e)
        
        return structure
    
    def _is_file_organized(self, file_path: Path) -> bool:
        """فحص ما إذا كان الملف منظم في المكان الصحيح"""
        relative_path = file_path.relative_to(self.project_path)
        
        for rule_name, rule in self.organization_rules.items():
            if self._file_matches_rule(file_path, rule):
                target_dir = rule.get('target_dir', '')
                
                if target_dir == '':
                    # ملف يجب أن يكون في الجذر
                    return len(relative_path.parts) == 1
                else:
                    # ملف يجب أن يكون في مجلد محدد
                    return relative_path.parts[0] == target_dir
        
        return False  # ملف غير مصنف
    
    def _file_matches_rule(self, file_path: Path, rule: Dict[str, Any]) -> bool:
        """فحص ما إذا كان الملف يطابق قاعدة معينة"""
        file_name = file_path.name
        
        # فحص الاستثناءات
        exclude_patterns = rule.get('exclude', [])
        for pattern in exclude_patterns:
            if fnmatch.fnmatch(file_name, pattern):
                return False
        
        # فحص الأنماط
        patterns = rule.get('patterns', [])
        for pattern in patterns:
            if fnmatch.fnmatch(file_name, pattern):
                return True
        
        # فحص الامتدادات
        extensions = rule.get('extensions', [])
        if file_path.suffix.lower() in extensions:
            return True
        
        return False
    
    def _analyze_directory_structure(self) -> Dict[str, Any]:
        """تحليل هيكل المجلدات"""
        structure = {}
        
        for item in self.project_path.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                structure[item.name] = {
                    'files_count': len(list(item.rglob('*.*'))),
                    'subdirs_count': len([d for d in item.iterdir() if d.is_dir()]),
                    'purpose': self._identify_directory_purpose(item)
                }
        
        return structure
    
    def _identify_directory_purpose(self, directory: Path) -> str:
        """تحديد الغرض من المجلد"""
        dir_name = directory.name.lower()
        
        purpose_mapping = {
            'src': 'source code',
            'source': 'source code',
            'tests': 'testing',
            'test': 'testing',
            'docs': 'documentation',
            'documentation': 'documentation',
            'config': 'configuration',
            'data': 'data files',
            'static': 'static files',
            'assets': 'static files',
            'logs': 'log files',
            'temp': 'temporary files',
            'archive': 'archived files',
            'backup': 'backup files'
        }
        
        return purpose_mapping.get(dir_name, 'unknown')
    
    def create_organization_plan(self) -> Dict[str, Any]:
        """إنشاء خطة التنظيم"""
        plan = {
            'moves_required': [],
            'directories_to_create': [],
            'files_to_move': 0,
            'estimated_time_minutes': 0
        }
        
        try:
            # فحص جميع الملفات
            for file_path in self.project_path.rglob('*.*'):
                if file_path.is_file():
                    target_location = self._get_target_location(file_path)
                    
                    if target_location and target_location != file_path.parent:
                        plan['moves_required'].append({
                            'source': str(file_path.relative_to(self.project_path)),
                            'target': str(target_location.relative_to(self.project_path)),
                            'reason': self._get_move_reason(file_path)
                        })
                        plan['files_to_move'] += 1
                        
                        # إضافة المجلد للإنشاء إذا لم يكن موجود
                        if not target_location.exists():
                            target_dir = str(target_location.relative_to(self.project_path))
                            if target_dir not in plan['directories_to_create']:
                                plan['directories_to_create'].append(target_dir)
            
            # تقدير الوقت (ثانية واحدة لكل ملف)
            plan['estimated_time_minutes'] = max(1, plan['files_to_move'] // 60)
            
        except Exception as e:
            plan['error'] = str(e)
        
        return plan
    
    def _get_target_location(self, file_path: Path) -> Optional[Path]:
        """تحديد الموقع المستهدف للملف"""
        for rule_name, rule in self.organization_rules.items():
            if self._file_matches_rule(file_path, rule):
                target_dir = rule.get('target_dir', '')
                
                if target_dir == '':
                    return self.project_path
                else:
                    return self.project_path / target_dir
        
        return None
    
    def _get_move_reason(self, file_path: Path) -> str:
        """الحصول على سبب النقل"""
        for rule_name, rule in self.organization_rules.items():
            if self._file_matches_rule(file_path, rule):
                return rule.get('description', f'ينتمي لفئة {rule_name}')
        
        return 'غير مصنف'
    
    def get_file_statistics(self) -> Dict[str, Any]:
        """إحصائيات الملفات"""
        stats = {
            'total_size_mb': 0,
            'largest_files': [],
            'file_type_distribution': {},
            'empty_directories': []
        }
        
        try:
            file_sizes = []
            
            for file_path in self.project_path.rglob('*.*'):
                if file_path.is_file():
                    size = file_path.stat().st_size
                    stats['total_size_mb'] += size
                    
                    file_sizes.append({
                        'path': str(file_path.relative_to(self.project_path)),
                        'size_mb': round(size / (1024 * 1024), 2)
                    })
                    
                    # توزيع أنواع الملفات
                    ext = file_path.suffix.lower()
                    if ext not in stats['file_type_distribution']:
                        stats['file_type_distribution'][ext] = {'count': 0, 'size_mb': 0}
                    
                    stats['file_type_distribution'][ext]['count'] += 1
                    stats['file_type_distribution'][ext]['size_mb'] += round(size / (1024 * 1024), 2)
            
            # تحويل الحجم الإجمالي
            stats['total_size_mb'] = round(stats['total_size_mb'] / (1024 * 1024), 2)
            
            # أكبر الملفات
            file_sizes.sort(key=lambda x: x['size_mb'], reverse=True)
            stats['largest_files'] = file_sizes[:10]
            
            # المجلدات الفارغة
            for dir_path in self.project_path.rglob('*'):
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    stats['empty_directories'].append(str(dir_path.relative_to(self.project_path)))
            
        except Exception as e:
            stats['error'] = str(e)
        
        return stats
    
    def generate_organization_recommendations(self) -> List[str]:
        """إنشاء توصيات التنظيم"""
        recommendations = []
        
        try:
            structure = self.analyze_current_structure()
            
            # توصيات بناء على الملفات غير المنظمة
            unorganized_count = len(structure.get('unorganized_files', []))
            if unorganized_count > 0:
                recommendations.append(f"تنظيم {unorganized_count} ملف غير منظم")
            
            # توصيات بناء على أنواع الملفات
            file_types = structure.get('file_types', {})
            if '.py' in file_types and file_types['.py'] > 5:
                recommendations.append("إنشاء مجلد src لملفات Python")
            
            if '.md' in file_types and file_types['.md'] > 2:
                recommendations.append("إنشاء مجلد docs للوثائق")
            
            if '.log' in file_types:
                recommendations.append("إنشاء مجلد logs لملفات السجلات")
            
            # توصيات عامة
            if structure.get('total_files', 0) > 20:
                recommendations.append("تنظيم الملفات في مجلدات فرعية")
            
        except Exception as e:
            recommendations.append(f"خطأ في إنشاء التوصيات: {e}")
        
        return recommendations
    
    def organize_files(self) -> Dict[str, Any]:
        """تنظيم الملفات فعلياً"""
        result = {
            'success': True,
            'files_moved': 0,
            'directories_created': 0,
            'errors': [],
            'backup_created': False
        }
        
        try:
            # إنشاء نسخة احتياطية إذا كان مطلوب
            if self.backup_before_organize:
                backup_path = self._create_backup()
                result['backup_created'] = backup_path is not None
                result['backup_path'] = str(backup_path) if backup_path else None
            
            # تنفيذ خطة التنظيم
            plan = self.create_organization_plan()
            
            # إنشاء المجلدات المطلوبة
            for dir_name in plan.get('directories_to_create', []):
                target_dir = self.project_path / dir_name
                if not self.dry_run:
                    target_dir.mkdir(parents=True, exist_ok=True)
                result['directories_created'] += 1
                self.log_action("إنشاء مجلد", str(target_dir))
            
            # نقل الملفات
            for move in plan.get('moves_required', []):
                try:
                    source = self.project_path / move['source']
                    target = self.project_path / move['target'] / source.name
                    
                    if not self.dry_run and source.exists():
                        target.parent.mkdir(parents=True, exist_ok=True)
                        shutil.move(str(source), str(target))
                    
                    result['files_moved'] += 1
                    self.log_action("نقل ملف", f"{move['source']} -> {move['target']}")
                    
                except Exception as e:
                    error_msg = f"خطأ في نقل {move['source']}: {e}"
                    result['errors'].append(error_msg)
                    self.log_action("خطأ في النقل", error_msg)
            
            if result['errors']:
                result['success'] = len(result['errors']) < result['files_moved']
            
        except Exception as e:
            result['success'] = False
            result['error'] = str(e)
            self.log_action("خطأ في تنظيم الملفات", str(e))
        
        return result
    
    def _create_backup(self) -> Optional[Path]:
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{self.project_path.name}_{timestamp}"
            backup_path = self.backup_dir / backup_name
            
            shutil.copytree(str(self.project_path), str(backup_path))
            self.log_action("تم إنشاء نسخة احتياطية", str(backup_path))
            
            return backup_path
            
        except Exception as e:
            self.log_action("خطأ في إنشاء النسخة الاحتياطية", str(e))
            return None
