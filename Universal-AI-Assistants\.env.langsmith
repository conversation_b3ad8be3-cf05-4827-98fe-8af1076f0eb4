# 🔗 إعداد متغيرات البيئة لـ LangSmith
# LangSmith Environment Variables Setup

# تفعيل تتبع LangSmith
export LANGCHAIN_TRACING_V2=true

# مفتاح API (تم الحصول عليه من https://smith.langchain.com/)
export LANGCHAIN_API_KEY="***************************************************"

# اسم المشروع في LangSmith
export LANGCHAIN_PROJECT="anubis-ai-system"

# نقطة النهاية للـ API
export LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"

# إعدادات إضافية
export LANGCHAIN_SESSION="anubis-session"
export LANGCHAIN_TAGS="anubis,ai-agents,ollama"

# للاستخدام في Windows PowerShell:
# $env:LANGCHAIN_TRACING_V2="true"
# $env:LANGCHAIN_PROJECT="anubis-ai-system"
# $env:LANGCHAIN_API_KEY="your-api-key-here"

echo "🔗 تم تحميل متغيرات البيئة لـ LangSmith"
