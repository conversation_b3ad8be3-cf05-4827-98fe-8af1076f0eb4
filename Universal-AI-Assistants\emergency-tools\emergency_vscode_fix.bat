@echo off
echo ========================================
echo    VS CODE EMERGENCY FIX - 72 PROCESSES
echo ========================================
echo.
echo Current situation: VS Code has 72+ processes running
echo This is a critical situation requiring immediate action!
echo.

echo [1] Quick Emergency Check
echo [2] Emergency Cleanup Tool  
echo [3] Detailed Analysis
echo [4] View Emergency Guide
echo [5] Force Close All VS Code (DANGEROUS)
echo [6] Safe Restart VS Code
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo.
    echo Running emergency check...
    python scripts/emergency_vscode_check.py
    pause
) else if "%choice%"=="2" (
    echo.
    echo Running emergency cleanup...
    python scripts/vscode_emergency_cleanup.py
    pause
) else if "%choice%"=="3" (
    echo.
    echo Running detailed analysis...
    python scripts/vscode_heavy_load_analyzer.py
    pause
) else if "%choice%"=="4" (
    echo.
    echo Opening emergency guide...
    start docs/vscode_72_processes_emergency_guide.md
    pause
) else if "%choice%"=="5" (
    echo.
    echo WARNING: This will force close ALL VS Code processes!
    echo You may lose unsaved work!
    set /p confirm="Type YES to confirm: "
    if /i "%confirm%"=="YES" (
        echo Force closing VS Code...
        taskkill /IM Code.exe /F /T
        echo Done. Wait 10 seconds before restarting VS Code.
    ) else (
        echo Cancelled.
    )
    pause
) else if "%choice%"=="6" (
    echo.
    echo Attempting safe restart...
    echo 1. Closing VS Code gracefully...
    taskkill /IM Code.exe /T
    echo 2. Waiting 10 seconds...
    timeout /t 10 /nobreak
    echo 3. Starting VS Code with minimal extensions...
    start "" "code" --disable-extensions
    echo Done! VS Code should start with minimal load.
    pause
) else (
    echo Invalid choice. Please run the script again.
    pause
)

echo.
echo ========================================
echo Emergency fix completed.
echo Monitor your process count regularly!
echo ========================================
pause
